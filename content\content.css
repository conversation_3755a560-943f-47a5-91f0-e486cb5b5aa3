/* AI SEO Optimizer - Content Script Styles */

/* SEO Highlights */
.seo-highlight {
    position: relative;
    outline: 2px solid #ff6b6b !important;
    outline-offset: 2px;
    background-color: rgba(255, 107, 107, 0.1) !important;
    transition: all 0.3s ease;
}

.seo-highlight:hover {
    outline-color: #ff5252 !important;
    background-color: rgba(255, 107, 107, 0.2) !important;
}

.seo-missing-alt {
    outline-color: #ff9800 !important;
    background-color: rgba(255, 152, 0, 0.1) !important;
}

.seo-empty-link {
    outline-color: #f44336 !important;
    background-color: rgba(244, 67, 54, 0.1) !important;
}

.seo-missing-h1 {
    outline-color: #9c27b0 !important;
    background-color: rgba(156, 39, 176, 0.1) !important;
}

.seo-multiple-h1 {
    outline-color: #e91e63 !important;
    background-color: rgba(233, 30, 99, 0.1) !important;
}

/* Keyword Highlights */
.seo-keyword-highlight {
    background-color: #ffeb3b !important;
    color: #333 !important;
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: 500;
}

/* Tooltips */
.seo-tooltip {
    position: absolute;
    z-index: 10000;
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    pointer-events: none;
}

.seo-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border: 5px solid transparent;
    border-top-color: #333;
}

/* SEO Overlay */
.seo-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.seo-overlay.hidden {
    display: none;
}

.seo-overlay-content {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    max-height: 80%;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.seo-overlay-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.seo-overlay-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.seo-close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.seo-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.seo-overlay-body {
    padding: 20px;
    max-height: 400px;
    overflow-y: auto;
}

/* SEO Suggestions */
.seo-suggestion {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 12px;
    transition: all 0.2s ease;
}

.seo-suggestion:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.suggestion-icon {
    font-size: 20px;
    margin-top: 2px;
}

.suggestion-content {
    flex: 1;
}

.suggestion-content h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.suggestion-content p {
    margin: 0 0 8px 0;
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}

.suggestion-priority {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.suggestion-priority.high {
    background-color: #ffebee;
    color: #c62828;
}

.suggestion-priority.medium {
    background-color: #fff3e0;
    color: #ef6c00;
}

.suggestion-priority.low {
    background-color: #e8f5e8;
    color: #2e7d32;
}

/* Selection Analysis */
.seo-selection-analysis {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    min-width: 200px;
    animation: slideInRight 0.3s ease;
}

.seo-selection-analysis .analysis-content h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.seo-selection-analysis .analysis-content p {
    margin: 0 0 4px 0;
    font-size: 12px;
    color: #666;
}

.seo-selection-analysis .analysis-content p:last-child {
    margin-bottom: 0;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* SEO Mode Active */
body.seo-mode-active {
    position: relative;
}

body.seo-mode-active::before {
    content: 'SEO Analysis Mode Active';
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: #667eea;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    z-index: 9999;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    animation: fadeInDown 0.3s ease;
}

@keyframes fadeInDown {
    from {
        transform: translateX(-50%) translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .seo-overlay-content {
        width: 95%;
        margin: 20px;
    }
    
    .seo-overlay-header {
        padding: 16px;
    }
    
    .seo-overlay-body {
        padding: 16px;
    }
    
    .seo-suggestion {
        padding: 12px;
    }
    
    .seo-selection-analysis {
        right: 10px;
        top: 10px;
        min-width: 180px;
    }
}

/* Print Styles */
@media print {
    .seo-highlight,
    .seo-tooltip,
    .seo-overlay,
    .seo-selection-analysis {
        display: none !important;
    }
    
    .seo-keyword-highlight {
        background: none !important;
        color: inherit !important;
        font-weight: normal !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .seo-highlight {
        outline-width: 3px !important;
    }
    
    .seo-tooltip {
        background: #000;
        border: 1px solid #fff;
    }
    
    .seo-keyword-highlight {
        background-color: #000 !important;
        color: #fff !important;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .seo-highlight,
    .seo-suggestion,
    .seo-selection-analysis {
        transition: none;
    }
    
    .seo-selection-analysis {
        animation: none;
    }
    
    body.seo-mode-active::before {
        animation: none;
    }
}
