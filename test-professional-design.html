<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI SEO Optimizer - Professional Design Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            color: #333;
        }
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            align-items: flex-start;
        }
        .popup-frame {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
            border: 1px solid #e5e7eb;
        }
        .info-panel {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }
        .status-badge {
            display: inline-block;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .status-badge.success {
            background: linear-gradient(135deg, #dcfce7, #bbf7d0);
            color: #166534;
            border: 1px solid #86efac;
        }
        .status-badge.info {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            color: #1e40af;
            border: 1px solid #60a5fa;
        }
        .improvements {
            background: linear-gradient(135deg, #f0f4ff, #e0e7ff);
            border: 2px solid #c7d2fe;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .improvements h3 {
            color: #4338ca;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .improvements ul {
            margin: 0;
            padding-left: 20px;
        }
        .improvements li {
            margin: 8px 0;
            color: #4338ca;
        }
        .code-snippet {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 15px 0;
            overflow-x: auto;
        }
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🎨 AI SEO Optimizer</h1>
            <p class="gradient-text">Professional Design Showcase</p>
            <div class="status-badge success">✅ Design Updated</div>
            <div class="status-badge info">🚀 Ready for Testing</div>
        </div>

        <div class="demo-grid">
            <div class="popup-frame">
                <iframe src="popup/popup.html" width="380" height="600" frameborder="0"></iframe>
            </div>

            <div class="info-panel">
                <h2 class="gradient-text">🎯 Design Improvements</h2>
                
                <div class="improvements">
                    <h3>✨ Visual Enhancements</h3>
                    <ul>
                        <li>Modern gradient backgrounds</li>
                        <li>Professional color scheme</li>
                        <li>Smooth animations & transitions</li>
                        <li>Enhanced typography</li>
                        <li>Improved spacing & layout</li>
                    </ul>
                </div>

                <h3>🔧 Technical Fixes</h3>
                <ul class="feature-list">
                    <li>
                        <div class="feature-icon">✓</div>
                        <span>Fixed popup size issues</span>
                    </li>
                    <li>
                        <div class="feature-icon">✓</div>
                        <span>Improved error handling</span>
                    </li>
                    <li>
                        <div class="feature-icon">✓</div>
                        <span>Enhanced Chrome API integration</span>
                    </li>
                    <li>
                        <div class="feature-icon">✓</div>
                        <span>Better responsive design</span>
                    </li>
                    <li>
                        <div class="feature-icon">✓</div>
                        <span>Professional UI components</span>
                    </li>
                </ul>

                <h3>🎨 Design Features</h3>
                <div class="code-snippet">
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --radius-xl: 16px;
}
                </div>

                <h3>📱 User Experience</h3>
                <ul class="feature-list">
                    <li>
                        <div class="feature-icon">🎯</div>
                        <span>Intuitive navigation</span>
                    </li>
                    <li>
                        <div class="feature-icon">⚡</div>
                        <span>Fast loading animations</span>
                    </li>
                    <li>
                        <div class="feature-icon">🎨</div>
                        <span>Consistent visual hierarchy</span>
                    </li>
                    <li>
                        <div class="feature-icon">📊</div>
                        <span>Clear data visualization</span>
                    </li>
                </ul>

                <div class="improvements">
                    <h3>🚀 Next Steps</h3>
                    <ul>
                        <li>Test in Chrome extension environment</li>
                        <li>Verify all functionality works</li>
                        <li>Add LLM providers and test analysis</li>
                        <li>Check responsive behavior</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('🎨 Professional Design Test Loaded');
        
        // Check if popup loads correctly
        const iframe = document.querySelector('iframe');
        iframe.onload = function() {
            console.log('✅ Popup loaded with professional design');
            console.log('📏 Frame dimensions:', iframe.width + 'x' + iframe.height);
        };
        
        iframe.onerror = function() {
            console.error('❌ Failed to load popup');
        };

        // Add some interactive effects
        document.querySelectorAll('.feature-list li').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.transform = 'translateX(5px)';
                this.style.transition = 'transform 0.2s ease';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.transform = 'translateX(0)';
            });
        });
    </script>
</body>
</html>
