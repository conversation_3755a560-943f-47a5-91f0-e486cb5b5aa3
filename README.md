# AI SEO Optimizer Chrome Extension

An AI-powered Chrome extension designed to help website owners, content creators, and digital marketers optimize their webpages for search engines.

## Features

### 🎯 Keyword Analysis
- AI-powered keyword analysis and suggestions
- Keyword density optimization
- Competitor keyword research
- Context-aware keyword recommendations

### 📝 Content Optimization
- Real-time content analysis
- SEO-friendly content suggestions
- Readability scoring
- Content structure optimization

### 🔍 Technical SEO Analysis
- Meta tag optimization
- Heading structure analysis
- Image alt text checking
- Internal/external link analysis

### 📊 Real-time Reporting
- Visual SEO health scores
- Actionable recommendations
- Progress tracking
- Historical analysis data

### 🤖 AI Integration
- OpenAI GPT-4 integration
- OpenRouter API support
- Intelligent content generation
- Context-aware suggestions

### 🌐 Multi-language Support
- Global market optimization
- Automatic content translation
- Localized SEO recommendations

## Installation

### Development Setup

1. Clone or download this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" in the top right
4. Click "Load unpacked" and select the extension directory
5. The extension will appear in your Chrome toolbar

### Configuration

1. Click the extension icon in your Chrome toolbar
2. Go to the Settings tab
3. Add your API keys:
   - **OpenAI API Key**: Required for AI-powered analysis
   - **OpenRouter API Key**: Optional alternative AI provider
4. Configure your SEO preferences:
   - Target keywords
   - Keyword density goals
   - Content optimization settings

## Usage

### Quick Analysis
1. Navigate to any webpage
2. Click the AI SEO Optimizer extension icon
3. Click "Analyze Current Page"
4. Review the SEO score and recommendations

### Detailed Analysis
1. Switch to the "Analysis" tab for detailed insights:
   - Meta tags analysis
   - Heading structure review
   - Keyword density report
   - Content quality assessment

### Keyword Generation
1. Click "Generate Keywords" on any page
2. AI will analyze the content and suggest relevant keywords
3. Keywords are automatically added to your target keywords list

### Visual Highlighting
- Right-click on any page and select "Analyze page with AI SEO Optimizer"
- SEO issues will be highlighted directly on the page
- Hover over highlighted elements for detailed explanations

## API Integration

### Required APIs
- **OpenAI API**: For content analysis and keyword generation
- **OpenRouter API**: Alternative AI provider (optional)

### Optional APIs (Future Integration)
- Google Search Console API
- Moz API
- SEMrush API

## File Structure

```
ai-seo-optimizer/
├── manifest.json              # Extension manifest
├── popup/
│   ├── popup.html            # Main popup interface
│   ├── popup.css             # Popup styling
│   └── popup.js              # Popup functionality
├── background/
│   └── background.js         # Background service worker
├── content/
│   ├── content.js            # Content script for page analysis
│   └── content.css           # Content script styling
├── assets/
│   └── icons/                # Extension icons
└── README.md                 # This file
```

## Development

### Prerequisites
- Chrome browser
- API keys for AI services
- Basic knowledge of Chrome extension development

### Key Components

#### Popup Interface (`popup/`)
- Main user interface with dashboard, analysis, and settings tabs
- Real-time SEO scoring and recommendations
- Settings management for API keys and preferences

#### Background Service Worker (`background/background.js`)
- Handles extension lifecycle events
- Manages API communications
- Processes page analysis requests
- Maintains analysis history

#### Content Script (`content/`)
- Extracts page data for analysis
- Highlights SEO issues on pages
- Provides visual feedback and suggestions
- Monitors page changes for dynamic content

### Adding New Features

1. **New Analysis Types**: Add analysis logic to `background.js` and update the UI in `popup.js`
2. **Additional APIs**: Extend the API integration in the background service worker
3. **UI Enhancements**: Modify the popup HTML/CSS and corresponding JavaScript
4. **Content Highlighting**: Add new highlight types in `content/content.js` and styles in `content/content.css`

## Privacy & Security

- All analysis is performed locally or through your configured API keys
- No user data is stored on external servers without explicit consent
- API keys are stored securely in Chrome's sync storage
- Page content is only sent to AI APIs when explicitly requested

## Troubleshooting

### Common Issues

1. **Extension not loading**: Ensure all files are present and manifest.json is valid
2. **API errors**: Check that your API keys are correctly configured in settings
3. **Analysis not working**: Verify the page allows content script injection
4. **Popup not opening**: Check for JavaScript errors in the browser console

### Debug Mode
1. Right-click the extension icon and select "Inspect popup"
2. Check the console for error messages
3. Use Chrome DevTools to debug content script issues

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, feature requests, or bug reports, please create an issue in the project repository.

## Roadmap

### Version 1.1
- [ ] Advanced keyword research tools
- [ ] Competitor analysis features
- [ ] Page speed optimization suggestions
- [ ] Backlink analysis integration

### Version 1.2
- [ ] Bulk page analysis
- [ ] SEO audit reports
- [ ] Integration with Google Search Console
- [ ] Advanced content generation tools

### Version 2.0
- [ ] Machine learning-based recommendations
- [ ] Multi-site management
- [ ] Team collaboration features
- [ ] Advanced analytics dashboard
