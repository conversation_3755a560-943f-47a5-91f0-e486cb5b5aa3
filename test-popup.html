<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Test page for AI SEO Optimizer Chrome extension with LLM provider management">
    <title>AI SEO Optimizer - Test Popup with LLM Management</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 20px;
            align-items: start;
        }
        .popup-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
            width: 380px;
            height: 600px;
        }
        .instructions {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✅ ";
            color: #28a745;
            font-weight: bold;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🚀 AI SEO Optimizer - LLM Provider Test</h1>
    
    <div class="test-container">
        <div class="instructions">
            <h2>🧪 Testing Instructions</h2>
            
            <div class="test-section">
                <h3>1. LLM Provider Management</h3>
                <ul class="feature-list">
                    <li>Click "Settings" tab in the popup</li>
                    <li>Click "+ Add Provider" button</li>
                    <li>Select different providers (OpenAI, Gemini, etc.)</li>
                    <li>Add API keys and test configuration</li>
                    <li>Set a default provider</li>
                    <li>Delete providers using the 🗑️ button</li>
                </ul>
            </div>

            <div class="test-section">
                <h3>2. Analysis Features</h3>
                <ul class="feature-list">
                    <li>Click "Dashboard" tab</li>
                    <li>Click "Analyze Current Page"</li>
                    <li>Check SEO scores and recommendations</li>
                    <li>Try "Generate Keywords" feature</li>
                    <li>Review the Analysis tab for details</li>
                </ul>
            </div>

            <div class="test-section">
                <h3>3. Supported LLM Providers</h3>
                <ul class="feature-list">
                    <li>🤖 OpenAI (GPT-4, GPT-3.5)</li>
                    <li>🧠 Anthropic (Claude 3)</li>
                    <li>💎 Google Gemini (Gemini Pro)</li>
                    <li>🌐 OpenRouter (Multiple models)</li>
                    <li>🏠 Ollama (Local hosting)</li>
                    <li>⚡ Groq (Fast inference)</li>
                    <li>⚙️ Custom (Any API)</li>
                </ul>
            </div>

            <div class="status success">
                <strong>✅ Fixed Issues:</strong><br>
                • Chrome storage API errors resolved<br>
                • Fallback support for testing<br>
                • Mock data for development<br>
                • Enhanced error handling
            </div>

            <div class="status info">
                <strong>💡 API Keys Needed:</strong><br>
                • OpenAI: <span class="code">sk-...</span><br>
                • Gemini: Get from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a><br>
                • Anthropic: <span class="code">sk-ant-...</span><br>
                • OpenRouter: <span class="code">sk-or-...</span>
            </div>

            <h3>🔧 Development Notes</h3>
            <p>This popup works in both contexts:</p>
            <ul>
                <li><strong>Extension Context:</strong> Full Chrome API access</li>
                <li><strong>Browser Context:</strong> Mock data for testing</li>
            </ul>
            
            <p>The extension automatically detects the context and provides appropriate functionality.</p>
        </div>

        <div class="popup-container">
            <iframe src="popup/popup.html" width="380" height="600" frameborder="0"></iframe>
        </div>
    </div>

    <script>
        console.log('Test page loaded - AI SEO Optimizer popup embedded for testing');
        
        // Add some test content for analysis
        document.addEventListener('DOMContentLoaded', function() {
            // Simulate extension environment detection
            if (typeof chrome === 'undefined') {
                console.log('Running in browser context - mock data will be used');
            } else {
                console.log('Chrome APIs detected - full functionality available');
            }
        });
    </script>
</body>
</html>
