# AI SEO Optimizer - Installation Guide

## Quick Start

### 1. Load the Extension in Chrome

1. **Open Chrome** and navigate to `chrome://extensions/`
2. **Enable Developer Mode** by toggling the switch in the top-right corner
3. **Click "Load unpacked"** and select the `Ai SEO Optimizer` folder
4. The extension should now appear in your Chrome toolbar

### 2. Configure API Keys

1. **Click the extension icon** in your Chrome toolbar
2. **Go to the Settings tab**
3. **Add your API keys:**
   - **OpenAI API Key**: Get from [OpenAI Platform](https://platform.openai.com/api-keys)
   - **OpenRouter API Key**: Get from [OpenRouter](https://openrouter.ai/) (optional)

### 3. Test the Extension

1. **Open the test page**: Navigate to `test/test-page.html` in your browser
2. **Click the extension icon**
3. **Click "Analyze Current Page"**
4. **Review the results** in the Dashboard and Analysis tabs

## Detailed Setup Instructions

### Prerequisites

- **Chrome Browser** (version 88 or higher)
- **API Keys** from supported AI providers:
  - OpenAI API key (required for full functionality)
  - OpenRouter API key (optional alternative)

### Getting API Keys

#### OpenAI API Key
1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key (it starts with `sk-`)

#### OpenRouter API Key (Optional)
1. Visit [OpenRouter](https://openrouter.ai/)
2. Sign up for an account
3. Go to the API Keys section
4. Generate a new API key
5. Copy the key

### Extension Configuration

#### Basic Settings
1. **Target Keywords**: Enter comma-separated keywords you want to optimize for
2. **Keyword Density**: Set your preferred keyword density percentage (1-5%)

#### Advanced Settings
- The extension stores settings in Chrome's sync storage
- Settings are automatically saved when you click "Save Settings"
- Use "Reset to Default" to clear all settings

### Features Overview

#### Dashboard Tab
- **SEO Score**: Overall page optimization score (0-100)
- **Component Scores**: Breakdown by keywords, content, and technical factors
- **Quick Actions**: Analyze page and generate keywords
- **Recommendations**: AI-powered suggestions for improvement

#### Analysis Tab
- **Meta Tags Analysis**: Title, description, and keyword optimization
- **Heading Structure**: H1-H6 hierarchy and optimization
- **Keyword Density**: Target keyword usage analysis
- **Content Quality**: Readability and structure assessment

#### Settings Tab
- **API Configuration**: Set up OpenAI and OpenRouter keys
- **SEO Preferences**: Configure target keywords and density goals
- **Save/Reset Options**: Manage your settings

### Usage Instructions

#### Analyzing a Page
1. Navigate to any webpage
2. Click the AI SEO Optimizer extension icon
3. Click "Analyze Current Page" in the Dashboard
4. Wait for analysis to complete
5. Review scores and recommendations

#### Generating Keywords
1. On any webpage, click the extension icon
2. Click "Generate Keywords" in the Dashboard
3. AI will analyze the page content and suggest relevant keywords
4. Keywords are automatically added to your target keywords list

#### Visual Page Analysis
1. Right-click on any webpage
2. Select "Analyze page with AI SEO Optimizer"
3. SEO issues will be highlighted directly on the page
4. Hover over highlighted elements for explanations

### Troubleshooting

#### Common Issues

**Extension not loading:**
- Ensure all files are present in the extension directory
- Check that manifest.json is valid
- Reload the extension in chrome://extensions/

**API errors:**
- Verify your API keys are correctly entered in Settings
- Check that you have sufficient API credits
- Ensure your API keys have the necessary permissions

**Analysis not working:**
- Make sure the webpage allows content script injection
- Check browser console for JavaScript errors
- Try refreshing the page and analyzing again

**Popup not opening:**
- Right-click the extension icon and select "Inspect popup"
- Check for errors in the developer console
- Ensure popup.html and related files are present

#### Debug Mode
1. Right-click the extension icon
2. Select "Inspect popup" to open developer tools
3. Check the Console tab for error messages
4. Use the Network tab to monitor API requests

### File Structure Reference

```
Ai SEO Optimizer/
├── manifest.json              # Extension configuration
├── popup/
│   ├── popup.html            # Main interface
│   ├── popup.css             # Styling
│   └── popup.js              # Functionality
├── background/
│   └── background.js         # Service worker
├── content/
│   ├── content.js            # Page analysis
│   └── content.css           # Page styling
├── utils/
│   └── ai-integration.js     # AI API integration
├── assets/
│   └── icons/                # Extension icons
├── test/
│   └── test-page.html        # Test page
└── README.md                 # Documentation
```

### Security Notes

- API keys are stored securely in Chrome's sync storage
- Page content is only sent to AI APIs when explicitly requested
- No user data is stored on external servers without consent
- All analysis can be performed locally when AI features are disabled

### Support

If you encounter issues:
1. Check this installation guide
2. Review the main README.md file
3. Check the browser console for error messages
4. Ensure all prerequisites are met

### Next Steps

After successful installation:
1. Test the extension on various websites
2. Experiment with different keyword configurations
3. Use the generated recommendations to improve your content
4. Monitor SEO improvements over time

## Development Mode

If you're developing or modifying the extension:

1. Make changes to the source files
2. Go to chrome://extensions/
3. Click the refresh icon on the AI SEO Optimizer extension
4. Test your changes

The extension will automatically reload when you refresh it in the extensions page.
