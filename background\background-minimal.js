// AI SEO Optimizer - Minimal Background Service Worker

console.log('AI SEO Optimizer background script starting...');

// Basic extension setup
chrome.runtime.onInstalled.addListener((details) => {
    console.log('AI SEO Optimizer installed:', details.reason);
    
    // Set default settings
    chrome.storage.sync.set({
        keywordDensity: 2,
        autoAnalysis: false,
        targetKeywords: '',
        openaiKey: '',
        openrouterKey: ''
    }).then(() => {
        console.log('Default settings saved');
    }).catch(error => {
        console.error('Error saving default settings:', error);
    });
});

// Handle messages from popup and content scripts
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('Received message:', request.action);
    
    switch (request.action) {
        case 'analyzePage':
            handleAnalyzePage(request.data, sendResponse);
            break;
            
        case 'generateKeywords':
            handleGenerateKeywords(request.data, sendResponse);
            break;
            
        case 'getPageData':
            handleGetPageData(sender.tab.id, sendResponse);
            break;
            
        default:
            sendResponse({ success: false, error: 'Unknown action: ' + request.action });
    }
    
    return true; // Keep message channel open for async responses
});

// Simple page analysis
async function handleAnalyzePage(data, sendResponse) {
    try {
        const pageData = data.pageData;
        const targetKeywords = data.targetKeywords || [];
        
        console.log('Analyzing page:', pageData.url);
        
        // Basic SEO analysis
        const analysis = {
            seoScore: calculateBasicScore(pageData),
            keywordScore: analyzeKeywords(pageData, targetKeywords),
            contentScore: analyzeContent(pageData),
            technicalScore: analyzeTechnical(pageData),
            recommendations: generateRecommendations(pageData, targetKeywords)
        };
        
        console.log('Analysis complete:', analysis);
        sendResponse({ success: true, data: analysis });
        
    } catch (error) {
        console.error('Error analyzing page:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// Simple keyword generation
async function handleGenerateKeywords(data, sendResponse) {
    try {
        const pageData = data.pageData;
        console.log('Generating keywords for:', pageData.url);
        
        const content = pageData.content || '';
        const words = content.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 3);

        // Count word frequency
        const wordCount = {};
        words.forEach(word => {
            wordCount[word] = (wordCount[word] || 0) + 1;
        });

        // Get top keywords
        const topWords = Object.entries(wordCount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([word]) => word);

        const keywords = {
            primaryKeywords: topWords.slice(0, 5),
            longTailKeywords: [],
            semanticKeywords: topWords.slice(5, 10)
        };
        
        console.log('Keywords generated:', keywords);
        sendResponse({ success: true, data: keywords });
        
    } catch (error) {
        console.error('Error generating keywords:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// Get page data using content script
async function handleGetPageData(tabId, sendResponse) {
    try {
        console.log('Getting page data for tab:', tabId);
        
        const results = await chrome.scripting.executeScript({
            target: { tabId: tabId },
            function: extractPageData
        });

        const pageData = results[0].result;
        console.log('Page data extracted:', pageData.url);
        sendResponse({ success: true, data: pageData });
        
    } catch (error) {
        console.error('Error getting page data:', error);
        sendResponse({ success: false, error: error.message });
    }
}

// Function to inject into page for data extraction
function extractPageData() {
    return {
        url: window.location.href,
        title: document.title,
        metaDescription: document.querySelector('meta[name="description"]')?.content || '',
        metaKeywords: document.querySelector('meta[name="keywords"]')?.content || '',
        headings: {
            h1: Array.from(document.querySelectorAll('h1')).map(h => ({ text: h.textContent.trim() })),
            h2: Array.from(document.querySelectorAll('h2')).map(h => ({ text: h.textContent.trim() })),
            h3: Array.from(document.querySelectorAll('h3')).map(h => ({ text: h.textContent.trim() }))
        },
        content: document.body.innerText,
        wordCount: document.body.innerText.split(/\s+/).filter(w => w.length > 0).length,
        images: Array.from(document.querySelectorAll('img')).map(img => ({
            src: img.src,
            alt: img.alt || '',
            title: img.title || ''
        })),
        links: Array.from(document.querySelectorAll('a')).map(link => ({
            href: link.href,
            text: link.textContent.trim(),
            internal: link.hostname === window.location.hostname
        }))
    };
}

// Basic scoring functions
function calculateBasicScore(pageData) {
    let score = 0;
    
    // Title analysis (25 points)
    if (pageData.title && pageData.title.length >= 30 && pageData.title.length <= 60) {
        score += 25;
    } else if (pageData.title && pageData.title.length > 0) {
        score += 10;
    }
    
    // Meta description (25 points)
    if (pageData.metaDescription && pageData.metaDescription.length >= 120 && pageData.metaDescription.length <= 160) {
        score += 25;
    } else if (pageData.metaDescription && pageData.metaDescription.length > 0) {
        score += 10;
    }
    
    // Content length (25 points)
    if (pageData.wordCount > 300) {
        score += 25;
    } else if (pageData.wordCount > 150) {
        score += 15;
    } else if (pageData.wordCount > 50) {
        score += 5;
    }
    
    // Images with alt text (25 points)
    if (pageData.images && pageData.images.length > 0) {
        const imagesWithAlt = pageData.images.filter(img => img.alt && img.alt.trim().length > 0).length;
        const altPercentage = (imagesWithAlt / pageData.images.length) * 100;
        if (altPercentage === 100) {
            score += 25;
        } else if (altPercentage >= 80) {
            score += 20;
        } else if (altPercentage >= 50) {
            score += 10;
        }
    } else {
        score += 25; // No images is fine
    }
    
    return Math.min(100, score);
}

function analyzeKeywords(pageData, targetKeywords) {
    if (!targetKeywords || targetKeywords.length === 0) {
        return 50; // Neutral score if no target keywords
    }

    let score = 0;
    const content = (pageData.content || '').toLowerCase();
    const title = (pageData.title || '').toLowerCase();

    targetKeywords.forEach(keyword => {
        const keywordLower = keyword.toLowerCase();
        
        // Check if keyword is in title (higher weight)
        if (title.includes(keywordLower)) {
            score += 40;
        }
        
        // Check if keyword is in content
        if (content.includes(keywordLower)) {
            score += 20;
        }
    });

    return Math.min(100, score / targetKeywords.length);
}

function analyzeContent(pageData) {
    let score = 0;
    
    // Word count scoring (40 points)
    if (pageData.wordCount >= 500) {
        score += 40;
    } else if (pageData.wordCount >= 300) {
        score += 30;
    } else if (pageData.wordCount >= 150) {
        score += 20;
    } else if (pageData.wordCount >= 50) {
        score += 10;
    }

    // Heading structure (60 points)
    if (pageData.headings) {
        // H1 analysis (30 points)
        if (pageData.headings.h1 && pageData.headings.h1.length === 1) {
            score += 30;
        } else if (pageData.headings.h1 && pageData.headings.h1.length > 1) {
            score += 10; // Multiple H1s is not ideal
        }
        
        // H2 analysis (30 points)
        if (pageData.headings.h2 && pageData.headings.h2.length > 0) {
            score += 30;
        }
    }

    return Math.min(100, score);
}

function analyzeTechnical(pageData) {
    let score = 0;

    // HTTPS check (30 points)
    if (pageData.url && pageData.url.startsWith('https://')) {
        score += 30;
    }

    // Meta description exists (35 points)
    if (pageData.metaDescription && pageData.metaDescription.length > 0) {
        score += 35;
    }

    // Title exists (35 points)
    if (pageData.title && pageData.title.length > 0) {
        score += 35;
    }

    return Math.min(100, score);
}

function generateRecommendations(pageData, targetKeywords) {
    const recommendations = [];

    // Title recommendations
    if (!pageData.title) {
        recommendations.push({
            type: 'title',
            title: 'Add page title',
            description: 'Page is missing a title tag',
            priority: 'high'
        });
    } else if (pageData.title.length < 30) {
        recommendations.push({
            type: 'title',
            title: 'Expand page title',
            description: 'Page title should be 30-60 characters long',
            priority: 'high'
        });
    } else if (pageData.title.length > 60) {
        recommendations.push({
            type: 'title',
            title: 'Shorten page title',
            description: 'Page title should be under 60 characters',
            priority: 'medium'
        });
    }

    // Meta description recommendations
    if (!pageData.metaDescription) {
        recommendations.push({
            type: 'meta',
            title: 'Add meta description',
            description: 'Page is missing a meta description',
            priority: 'high'
        });
    } else if (pageData.metaDescription.length < 120) {
        recommendations.push({
            type: 'meta',
            title: 'Expand meta description',
            description: 'Meta description should be 120-160 characters',
            priority: 'medium'
        });
    } else if (pageData.metaDescription.length > 160) {
        recommendations.push({
            type: 'meta',
            title: 'Shorten meta description',
            description: 'Meta description should be under 160 characters',
            priority: 'medium'
        });
    }

    // Content recommendations
    if (pageData.wordCount < 300) {
        recommendations.push({
            type: 'content',
            title: 'Increase content length',
            description: `Content has ${pageData.wordCount} words. Aim for at least 300 words.`,
            priority: 'medium'
        });
    }

    // Heading recommendations
    if (pageData.headings) {
        if (!pageData.headings.h1 || pageData.headings.h1.length === 0) {
            recommendations.push({
                type: 'heading',
                title: 'Add H1 heading',
                description: 'Page should have exactly one H1 heading',
                priority: 'high'
            });
        } else if (pageData.headings.h1.length > 1) {
            recommendations.push({
                type: 'heading',
                title: 'Use only one H1',
                description: 'Page should have only one H1 heading',
                priority: 'medium'
            });
        }

        if (!pageData.headings.h2 || pageData.headings.h2.length === 0) {
            recommendations.push({
                type: 'heading',
                title: 'Add H2 subheadings',
                description: 'Use H2 tags to structure your content',
                priority: 'low'
            });
        }
    }

    // Image recommendations
    if (pageData.images && pageData.images.length > 0) {
        const imagesWithoutAlt = pageData.images.filter(img => !img.alt || img.alt.trim().length === 0).length;
        if (imagesWithoutAlt > 0) {
            recommendations.push({
                type: 'image',
                title: 'Add alt text to images',
                description: `${imagesWithoutAlt} of ${pageData.images.length} images are missing alt text`,
                priority: 'medium'
            });
        }
    }

    return recommendations;
}

console.log('AI SEO Optimizer background script loaded successfully');
