<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="This is a test page for the AI SEO Optimizer Chrome extension. It contains various SEO elements to test the analysis functionality.">
    <meta name="keywords" content="seo, test, optimization, chrome extension, ai">
    <title>AI SEO Optimizer Test Page - SEO Analysis Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }
        .content-section {
            margin-bottom: 30px;
        }
        .highlight {
            background-color: #f0f8ff;
            padding: 15px;
            border-left: 4px solid #667eea;
            margin: 20px 0;
        }
        .image-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .test-image {
            width: 100%;
            height: 150px;
            background: #f0f0f0;
            border: 2px dashed #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI SEO Optimizer Test Page</h1>
        <p>Welcome to the comprehensive test page for the AI SEO Optimizer Chrome extension!</p>
    </div>

    <main>
        <section class="content-section">
            <h2>About This Test Page</h2>
            <p>This test page is specifically designed to evaluate the <strong>AI SEO Optimizer</strong> Chrome extension's capabilities. It includes various SEO elements that the extension should analyze, including proper heading structure, meta tags, keyword usage, and content optimization opportunities.</p>
            
            <div class="highlight">
                <p><strong>Testing Instructions:</strong> Load this page in Chrome with the AI SEO Optimizer extension installed, then click the extension icon to analyze this page's SEO performance.</p>
            </div>
        </section>

        <section class="content-section">
            <h2>Content Optimization Testing</h2>
            <p>This section contains content specifically designed to test the extension's content analysis capabilities. The AI SEO Optimizer should be able to analyze keyword density, content structure, and readability scores.</p>
            
            <h3>Keyword Density Analysis</h3>
            <p>The term "SEO optimization" appears multiple times throughout this content to test keyword density calculations. SEO optimization is crucial for improving search engine rankings and driving organic traffic to websites.</p>
            
            <h3>Content Structure Evaluation</h3>
            <p>This page uses proper heading hierarchy (H1, H2, H3) to test the extension's ability to analyze content structure. Good content structure is essential for both user experience and search engine optimization.</p>
            
            <ul>
                <li>Proper heading hierarchy (H1 → H2 → H3)</li>
                <li>Descriptive meta tags and title</li>
                <li>Keyword-rich content</li>
                <li>Internal and external links</li>
                <li>Image optimization opportunities</li>
            </ul>
        </section>

        <section class="content-section">
            <h2>Technical SEO Elements</h2>
            <p>This section tests various technical SEO elements that the extension should identify and analyze:</p>
            
            <h3>Image Optimization Testing</h3>
            <p>The following images test different alt text scenarios:</p>
            
            <div class="image-gallery">
                <div class="test-image">
                    <span>Image with proper alt text</span>
                </div>
                <div class="test-image">
                    <span>Image missing alt text</span>
                </div>
                <div class="test-image">
                    <span>Decorative image</span>
                </div>
            </div>
            
            <!-- Test images for alt text analysis -->
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzY2N2VlYSIvPjx0ZXh0IHg9IjEwMCIgeT0iNTUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkdvb2QgQWx0IFRleHQ8L3RleHQ+PC9zdmc+" alt="AI SEO Optimizer test image with proper alt text describing the content" style="display: none;">
            
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y0NDMzNiIvPjx0ZXh0IHg9IjEwMCIgeT0iNTUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPk5vIEFsdCBUZXh0PC90ZXh0Pjwvc3ZnPg==" style="display: none;">
            
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzRjYWY1MCIvPjx0ZXh0IHg9IjEwMCIgeT0iNTUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkRlY29yYXRpdmU8L3RleHQ+PC9zdmc+" alt="" role="presentation" style="display: none;">
        </section>

        <section class="content-section">
            <h2>Link Analysis Testing</h2>
            <p>This section contains various types of links for the extension to analyze:</p>
            
            <ul>
                <li><a href="#internal-link">Internal link (same page)</a></li>
                <li><a href="https://example.com" target="_blank" rel="noopener">External link with proper attributes</a></li>
                <li><a href="https://spam-site.com">External link without nofollow</a></li>
                <li><a href="#">Empty link</a></li>
                <li><a href="">Link with empty href</a></li>
                <li><a>Link without href attribute</a></li>
            </ul>
        </section>

        <section class="content-section">
            <h2>Content Quality Assessment</h2>
            <p>This final section provides substantial content for the AI to analyze for quality, readability, and SEO effectiveness. The content discusses various aspects of search engine optimization, digital marketing strategies, and content creation best practices.</p>
            
            <p>Search engine optimization is a complex field that requires understanding of both technical and content-related factors. Modern SEO practices focus on creating high-quality, user-focused content that naturally incorporates relevant keywords and provides genuine value to readers.</p>
            
            <p>Content creators and digital marketers must balance keyword optimization with readability and user experience. The best SEO content is that which serves the user's intent while also being discoverable by search engines through proper technical implementation and strategic keyword usage.</p>
            
            <h3>Key SEO Factors to Consider</h3>
            <ol>
                <li><strong>Keyword Research:</strong> Understanding what terms your audience searches for</li>
                <li><strong>Content Quality:</strong> Creating comprehensive, valuable content</li>
                <li><strong>Technical SEO:</strong> Ensuring proper site structure and performance</li>
                <li><strong>User Experience:</strong> Making content accessible and engaging</li>
                <li><strong>Link Building:</strong> Earning quality backlinks from authoritative sources</li>
            </ol>
        </section>
    </main>

    <footer style="margin-top: 50px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
        <p><strong>Test Page Summary:</strong> This page contains approximately 500+ words, proper heading structure, meta tags, various link types, image optimization test cases, and keyword-rich content for comprehensive SEO analysis testing.</p>
        <p><em>Use this page to test all features of the AI SEO Optimizer Chrome extension.</em></p>
    </footer>

    <script>
        // Add some dynamic content for testing
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI SEO Optimizer test page loaded successfully');
            
            // Add a timestamp for testing dynamic content detection
            const timestamp = document.createElement('p');
            timestamp.textContent = 'Page loaded at: ' + new Date().toLocaleString();
            timestamp.style.fontSize = '12px';
            timestamp.style.color = '#666';
            document.querySelector('footer').appendChild(timestamp);
        });
    </script>
</body>
</html>
