// AI SEO Optimizer - Background Service Worker

// Import AI Integration and SEO Analyzer modules
importScripts('utils/ai-integration.js');
importScripts('utils/seo-analyzer.js');

class SEOOptimizerBackground {
    constructor() {
        this.aiIntegration = new AIIntegration();
        this.seoAnalyzer = new SEOAnalyzer();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupContextMenus();
    }

    setupEventListeners() {
        // Extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                this.handleInstall();
            } else if (details.reason === 'update') {
                this.handleUpdate(details.previousVersion);
            }
        });

        // Tab updates for automatic analysis
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            if (changeInfo.status === 'complete' && tab.url) {
                this.handleTabUpdate(tabId, tab);
            }
        });

        // Message handling from popup and content scripts
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Storage changes
        chrome.storage.onChanged.addListener((changes, namespace) => {
            this.handleStorageChange(changes, namespace);
        });
    }

    setupContextMenus() {
        chrome.contextMenus.create({
            id: 'analyze-page',
            title: 'Analyze page with AI SEO Optimizer',
            contexts: ['page']
        });

        chrome.contextMenus.create({
            id: 'analyze-selection',
            title: 'Analyze selected text',
            contexts: ['selection']
        });

        chrome.contextMenus.onClicked.addListener((info, tab) => {
            this.handleContextMenuClick(info, tab);
        });
    }

    handleInstall() {
        console.log('AI SEO Optimizer installed');
        
        // Set default settings
        chrome.storage.sync.set({
            keywordDensity: 2,
            autoAnalysis: false,
            targetKeywords: '',
            analysisHistory: []
        });

        // Open welcome page
        chrome.tabs.create({
            url: chrome.runtime.getURL('welcome/welcome.html')
        });
    }

    handleUpdate(previousVersion) {
        console.log(`AI SEO Optimizer updated from ${previousVersion}`);
        
        // Handle version-specific updates
        this.migrateSettings(previousVersion);
    }

    async handleTabUpdate(tabId, tab) {
        try {
            // Check if auto-analysis is enabled
            const settings = await chrome.storage.sync.get(['autoAnalysis']);
            
            if (settings.autoAnalysis && this.shouldAnalyzeUrl(tab.url)) {
                // Perform background analysis
                await this.performBackgroundAnalysis(tabId, tab);
            }
        } catch (error) {
            console.error('Error in tab update handler:', error);
        }
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'analyzePage':
                    const analysis = await this.analyzePage(
                        request.data.pageData,
                        request.data.targetKeywords || []
                    );
                    sendResponse({ success: true, data: analysis });
                    break;

                case 'generateKeywords':
                    const keywords = await this.generateKeywords(request.data.pageData);
                    sendResponse({ success: true, data: keywords });
                    break;

                case 'getPageData':
                    const pageData = await this.getPageData(sender.tab.id);
                    sendResponse({ success: true, data: pageData });
                    break;

                case 'saveAnalysis':
                    await this.saveAnalysisToHistory(request.data);
                    sendResponse({ success: true });
                    break;

                case 'settingsUpdated':
                    await this.handleSettingsUpdate(request.settings);
                    sendResponse({ success: true });
                    break;

                case 'getAnalysisHistory':
                    const history = await this.getAnalysisHistory();
                    sendResponse({ success: true, data: history });
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    handleStorageChange(changes, namespace) {
        if (namespace === 'sync') {
            // Handle settings changes
            if (changes.autoAnalysis) {
                console.log('Auto-analysis setting changed:', changes.autoAnalysis.newValue);
            }
        }
    }

    async handleContextMenuClick(info, tab) {
        try {
            switch (info.menuItemId) {
                case 'analyze-page':
                    await this.triggerPageAnalysis(tab.id);
                    break;

                case 'analyze-selection':
                    await this.analyzeSelectedText(info.selectionText, tab.id);
                    break;
            }
        } catch (error) {
            console.error('Error handling context menu click:', error);
        }
    }

    shouldAnalyzeUrl(url) {
        // Skip analysis for certain URLs
        const skipPatterns = [
            'chrome://',
            'chrome-extension://',
            'moz-extension://',
            'about:',
            'file://',
            'data:'
        ];

        return !skipPatterns.some(pattern => url.startsWith(pattern));
    }

    async performBackgroundAnalysis(tabId, tab) {
        try {
            // Get page data
            const pageData = await this.getPageData(tabId);
            
            // Perform basic analysis
            const analysis = await this.analyzePage(pageData);
            
            // Store analysis results
            await this.saveAnalysisToHistory({
                url: tab.url,
                title: tab.title,
                analysis: analysis,
                timestamp: Date.now()
            });

            // Show badge with SEO score
            chrome.action.setBadgeText({
                text: analysis.seoScore.toString(),
                tabId: tabId
            });

            chrome.action.setBadgeBackgroundColor({
                color: this.getScoreColor(analysis.seoScore),
                tabId: tabId
            });

        } catch (error) {
            console.error('Error in background analysis:', error);
        }
    }

    async getPageData(tabId) {
        const results = await chrome.scripting.executeScript({
            target: { tabId: tabId },
            function: () => {
                return {
                    url: window.location.href,
                    title: document.title,
                    metaDescription: document.querySelector('meta[name="description"]')?.content || '',
                    metaKeywords: document.querySelector('meta[name="keywords"]')?.content || '',
                    headings: {
                        h1: Array.from(document.querySelectorAll('h1')).map(h => h.textContent.trim()),
                        h2: Array.from(document.querySelectorAll('h2')).map(h => h.textContent.trim()),
                        h3: Array.from(document.querySelectorAll('h3')).map(h => h.textContent.trim())
                    },
                    content: document.body.innerText,
                    wordCount: document.body.innerText.split(/\s+/).length,
                    images: Array.from(document.querySelectorAll('img')).map(img => ({
                        src: img.src,
                        alt: img.alt || '',
                        title: img.title || ''
                    })),
                    links: Array.from(document.querySelectorAll('a')).map(link => ({
                        href: link.href,
                        text: link.textContent.trim(),
                        internal: link.hostname === window.location.hostname
                    }))
                };
            }
        });

        return results[0].result;
    }

    async analyzePage(pageData, targetKeywords = []) {
        try {
            // Initialize AI integration
            await this.aiIntegration.initialize();

            // Perform comprehensive SEO analysis using SEOAnalyzer
            const seoAnalysis = this.seoAnalyzer.analyzePageSEO(pageData, targetKeywords);

            // Convert SEOAnalyzer results to expected format
            const basicAnalysis = {
                seoScore: this.calculateOverallSEOScore(seoAnalysis),
                keywordScore: this.calculateKeywordScore(seoAnalysis),
                contentScore: this.calculateContentScore(seoAnalysis),
                technicalScore: this.calculateTechnicalScore(seoAnalysis),
                recommendations: seoAnalysis.recommendations,
                detailedAnalysis: seoAnalysis // Include full analysis for advanced features
            };

            // If AI is available, enhance with AI analysis
            if (this.aiIntegration.isAvailable()) {
                try {
                    const aiAnalysis = await this.aiIntegration.analyzeContent(pageData, targetKeywords);

                    // Merge SEO analyzer and AI analysis
                    return {
                        seoScore: Math.round((basicAnalysis.seoScore + aiAnalysis.seoScore) / 2),
                        keywordScore: Math.round((basicAnalysis.keywordScore + aiAnalysis.keywordScore) / 2),
                        contentScore: Math.round((basicAnalysis.contentScore + aiAnalysis.contentScore) / 2),
                        technicalScore: Math.round((basicAnalysis.technicalScore + aiAnalysis.technicalScore) / 2),
                        recommendations: [
                            ...basicAnalysis.recommendations,
                            ...aiAnalysis.recommendations
                        ].slice(0, 10), // Limit to top 10 recommendations
                        detailedAnalysis: basicAnalysis.detailedAnalysis
                    };
                } catch (aiError) {
                    console.warn('AI analysis failed, using SEO analyzer results:', aiError);
                    return basicAnalysis;
                }
            } else {
                return basicAnalysis;
            }
        } catch (error) {
            console.error('Error in analyzePage:', error);
            return this.getErrorAnalysis();
        }
    }

    async performBasicAnalysis(pageData) {
        const analysis = {
            seoScore: 0,
            keywordScore: 0,
            contentScore: 0,
            technicalScore: 0,
            recommendations: []
        };

        // Analyze title
        if (!pageData.title || pageData.title.length < 30) {
            analysis.recommendations.push({
                type: 'title',
                title: 'Optimize page title',
                description: 'Page title should be 30-60 characters long',
                priority: 'high'
            });
        } else {
            analysis.technicalScore += 20;
        }

        // Analyze meta description
        if (!pageData.metaDescription) {
            analysis.recommendations.push({
                type: 'meta',
                title: 'Add meta description',
                description: 'Page is missing a meta description',
                priority: 'high'
            });
        } else if (pageData.metaDescription.length < 120 || pageData.metaDescription.length > 160) {
            analysis.recommendations.push({
                type: 'meta',
                title: 'Optimize meta description length',
                description: 'Meta description should be 120-160 characters',
                priority: 'medium'
            });
        } else {
            analysis.technicalScore += 20;
        }

        // Analyze headings
        if (pageData.headings.h1.length === 0) {
            analysis.recommendations.push({
                type: 'heading',
                title: 'Add H1 heading',
                description: 'Page should have exactly one H1 heading',
                priority: 'high'
            });
        } else if (pageData.headings.h1.length > 1) {
            analysis.recommendations.push({
                type: 'heading',
                title: 'Multiple H1 headings found',
                description: 'Page should have only one H1 heading',
                priority: 'medium'
            });
        } else {
            analysis.contentScore += 25;
        }

        // Analyze content length
        if (pageData.wordCount < 300) {
            analysis.recommendations.push({
                type: 'content',
                title: 'Increase content length',
                description: 'Content should be at least 300 words for better SEO',
                priority: 'medium'
            });
        } else {
            analysis.contentScore += 25;
        }

        // Analyze images
        const imagesWithoutAlt = pageData.images.filter(img => !img.alt).length;
        if (imagesWithoutAlt > 0) {
            analysis.recommendations.push({
                type: 'image',
                title: 'Add alt text to images',
                description: `${imagesWithoutAlt} images are missing alt text`,
                priority: 'medium'
            });
        } else if (pageData.images.length > 0) {
            analysis.technicalScore += 20;
        }

        // Calculate overall score
        analysis.seoScore = Math.round((analysis.keywordScore + analysis.contentScore + analysis.technicalScore) / 3);

        return analysis;
    }

    calculateOverallSEOScore(seoAnalysis) {
        // Calculate weighted average of all component scores
        const titleScore = seoAnalysis.title.score || 0;
        const metaScore = seoAnalysis.metaDescription.score || 0;
        const headingScore = seoAnalysis.headings.score || 0;
        const contentScore = seoAnalysis.content.score || 0;
        const imageScore = seoAnalysis.images.score || 0;
        const linkScore = seoAnalysis.links.score || 0;
        const technicalScore = seoAnalysis.technical.score || 0;

        // Weighted calculation (title and content are more important)
        const weightedScore = (
            titleScore * 0.2 +
            metaScore * 0.15 +
            headingScore * 0.15 +
            contentScore * 0.25 +
            imageScore * 0.1 +
            linkScore * 0.1 +
            technicalScore * 0.05
        );

        return Math.round(weightedScore);
    }

    calculateKeywordScore(seoAnalysis) {
        const keywordAnalysis = seoAnalysis.keywords;
        return keywordAnalysis.overallScore || 0;
    }

    calculateContentScore(seoAnalysis) {
        const contentScore = seoAnalysis.content.score || 0;
        const readabilityScore = seoAnalysis.readability.score || 0;
        return Math.round((contentScore + readabilityScore) / 2);
    }

    calculateTechnicalScore(seoAnalysis) {
        const technicalScore = seoAnalysis.technical.score || 0;
        const headingScore = seoAnalysis.headings.score || 0;
        const imageScore = seoAnalysis.images.score || 0;
        return Math.round((technicalScore + headingScore + imageScore) / 3);
    }

    getErrorAnalysis() {
        return {
            seoScore: 0,
            keywordScore: 0,
            contentScore: 0,
            technicalScore: 0,
            recommendations: [
                {
                    type: 'error',
                    title: 'Analysis Error',
                    description: 'Unable to analyze page. Please try again.',
                    priority: 'high'
                }
            ]
        };
    }

    async generateKeywords(pageData) {
        try {
            // Initialize AI integration
            await this.aiIntegration.initialize();

            // If AI is available, use AI-powered keyword generation
            if (this.aiIntegration.isAvailable()) {
                try {
                    return await this.aiIntegration.generateKeywords(pageData);
                } catch (aiError) {
                    console.warn('AI keyword generation failed, using basic method:', aiError);
                    return this.generateBasicKeywords(pageData);
                }
            } else {
                return this.generateBasicKeywords(pageData);
            }
        } catch (error) {
            console.error('Error generating keywords:', error);
            return this.getFallbackKeywords();
        }
    }

    generateBasicKeywords(pageData) {
        // Basic keyword extraction from content
        const words = pageData.content.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 3);

        // Count word frequency
        const wordCount = {};
        words.forEach(word => {
            wordCount[word] = (wordCount[word] || 0) + 1;
        });

        // Get top keywords
        const keywords = Object.entries(wordCount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([word]) => word);

        return {
            primaryKeywords: keywords.slice(0, 5),
            longTailKeywords: [],
            semanticKeywords: keywords.slice(5, 10)
        };
    }

    getFallbackKeywords() {
        return {
            primaryKeywords: ['seo', 'optimization', 'content', 'marketing', 'website'],
            longTailKeywords: ['seo optimization tips', 'content marketing strategy'],
            semanticKeywords: ['search engine', 'digital marketing', 'web content']
        };
    }

    async triggerPageAnalysis(tabId) {
        try {
            const pageData = await this.getPageData(tabId);
            const analysis = await this.analyzePage(pageData);
            
            // Send results to popup if open
            chrome.runtime.sendMessage({
                action: 'analysisComplete',
                data: analysis
            });

        } catch (error) {
            console.error('Error triggering page analysis:', error);
        }
    }

    async analyzeSelectedText(text, tabId) {
        // Analyze selected text for keyword density, readability, etc.
        const analysis = {
            wordCount: text.split(/\s+/).length,
            characterCount: text.length,
            readabilityScore: this.calculateReadabilityScore(text)
        };

        // Send results to content script or popup
        chrome.tabs.sendMessage(tabId, {
            action: 'selectionAnalysis',
            data: analysis
        });
    }

    calculateReadabilityScore(text) {
        // Simple readability calculation (Flesch Reading Ease approximation)
        const sentences = text.split(/[.!?]+/).length;
        const words = text.split(/\s+/).length;
        const syllables = this.countSyllables(text);

        if (sentences === 0 || words === 0) return 0;

        const score = 206.835 - (1.015 * (words / sentences)) - (84.6 * (syllables / words));
        return Math.max(0, Math.min(100, Math.round(score)));
    }

    countSyllables(text) {
        // Simple syllable counting
        return text.toLowerCase()
            .replace(/[^a-z]/g, '')
            .replace(/[aeiou]{2,}/g, 'a')
            .replace(/[^aeiou]/g, '')
            .length || 1;
    }

    async saveAnalysisToHistory(analysisData) {
        try {
            const result = await chrome.storage.local.get(['analysisHistory']);
            const history = result.analysisHistory || [];
            
            // Add new analysis
            history.unshift(analysisData);
            
            // Keep only last 50 analyses
            if (history.length > 50) {
                history.splice(50);
            }
            
            await chrome.storage.local.set({ analysisHistory: history });
        } catch (error) {
            console.error('Error saving analysis to history:', error);
        }
    }

    getScoreColor(score) {
        if (score >= 80) return '#4CAF50'; // Green
        if (score >= 60) return '#FF9800'; // Orange
        return '#F44336'; // Red
    }

    async handleSettingsUpdate(settings) {
        try {
            // Update badge display based on settings
            if (settings.showBadge) {
                // Enable badge display for all tabs
                const tabs = await chrome.tabs.query({});
                tabs.forEach(tab => {
                    if (this.shouldAnalyzeUrl(tab.url)) {
                        chrome.action.setBadgeText({ text: '', tabId: tab.id });
                    }
                });
            } else {
                // Clear all badges
                const tabs = await chrome.tabs.query({});
                tabs.forEach(tab => {
                    chrome.action.setBadgeText({ text: '', tabId: tab.id });
                });
            }

            // Update auto-analysis behavior
            if (settings.autoAnalysis) {
                console.log('Auto-analysis enabled');
            } else {
                console.log('Auto-analysis disabled');
            }

            // Update AI integration settings
            await this.aiIntegration.initialize();

        } catch (error) {
            console.error('Error handling settings update:', error);
        }
    }

    async getAnalysisHistory() {
        try {
            const result = await chrome.storage.local.get(['analysisHistory']);
            return result.analysisHistory || [];
        } catch (error) {
            console.error('Error getting analysis history:', error);
            return [];
        }
    }

    async clearAnalysisHistory() {
        try {
            await chrome.storage.local.set({ analysisHistory: [] });
            return true;
        } catch (error) {
            console.error('Error clearing analysis history:', error);
            return false;
        }
    }

    async getTrackedDomains() {
        try {
            const settings = await chrome.storage.sync.get(['trackedDomains']);
            const domains = settings.trackedDomains || '';
            return domains.split(',').map(d => d.trim()).filter(d => d.length > 0);
        } catch (error) {
            console.error('Error getting tracked domains:', error);
            return [];
        }
    }

    async isTrackedDomain(url) {
        try {
            const trackedDomains = await this.getTrackedDomains();
            if (trackedDomains.length === 0) return true; // Track all if none specified

            const urlDomain = new URL(url).hostname;
            return trackedDomains.some(domain => urlDomain.includes(domain));
        } catch (error) {
            return false;
        }
    }

    migrateSettings(previousVersion) {
        // Handle settings migration for version updates
        console.log(`Migrating settings from version ${previousVersion}`);
    }
}

// Initialize background service worker
new SEOOptimizerBackground();
