<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="This is a simple test page for the AI SEO Optimizer Chrome extension. Test your extension here!">
    <title>AI SEO Optimizer Test Page - Quick Testing</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .instructions {
            background: #f0f8ff;
            padding: 15px;
            border-left: 4px solid #667eea;
            margin: 20px 0;
        }
        .good-example {
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .bad-example {
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI SEO Optimizer Test Page</h1>
        <p>Use this page to test your Chrome extension!</p>
    </div>

    <div class="instructions">
        <h3>🚀 How to Test:</h3>
        <ol>
            <li>Make sure the AI SEO Optimizer extension is loaded in Chrome</li>
            <li>Click the extension icon in your toolbar (purple "SEO" icon)</li>
            <li>Click "Analyze Current Page" in the popup</li>
            <li>Review the SEO scores and recommendations</li>
            <li>Try the "Generate Keywords" feature</li>
            <li>Check the Analysis tab for detailed results</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Content for SEO Testing</h2>
        <p>This page contains various SEO elements to test the extension's analysis capabilities. The AI SEO Optimizer should be able to analyze keyword density, content structure, and provide actionable recommendations for improvement.</p>
        
        <h3>SEO Best Practices Example</h3>
        <div class="good-example">
            <strong>✅ Good:</strong> This page has a proper title tag, meta description, and heading structure (H1 → H2 → H3).
        </div>
        
        <h3>Common SEO Issues</h3>
        <div class="bad-example">
            <strong>❌ Issues to detect:</strong> The extension should identify areas for improvement like keyword optimization and content length.
        </div>
        
        <p>The extension should analyze this content and provide recommendations for SEO optimization, content marketing strategies, and technical improvements.</p>
    </div>

    <div class="test-section">
        <h2>Images and Media</h2>
        <p>Testing image optimization:</p>
        
        <!-- Good image with alt text -->
        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iIzY2N2VlYSIvPjx0ZXh0IHg9IjEwMCIgeT0iNTUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkdvb2QgSW1hZ2U8L3RleHQ+PC9zdmc+" alt="AI SEO Optimizer test image with proper alt text" style="max-width: 200px; margin: 10px;">
        
        <!-- Bad image without alt text -->
        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0iI2Y0NDMzNiIvPjx0ZXh0IHg9IjEwMCIgeT0iNTUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkJhZCBJbWFnZTwvdGV4dD48L3N2Zz4=" style="max-width: 200px; margin: 10px;">
    </div>

    <div class="test-section">
        <h2>Links and Navigation</h2>
        <p>Testing link analysis:</p>
        <ul>
            <li><a href="#internal-link">Internal link (same page)</a></li>
            <li><a href="https://example.com" target="_blank" rel="noopener">External link with proper attributes</a></li>
            <li><a href="#">Empty link for testing</a></li>
        </ul>
    </div>

    <div class="test-section">
        <h2>Expected Results</h2>
        <p>When you analyze this page, the extension should:</p>
        <ul>
            <li>✅ Detect the proper title tag (30-60 characters)</li>
            <li>✅ Find the meta description (120-160 characters)</li>
            <li>✅ Identify the H1, H2, H3 heading structure</li>
            <li>✅ Count approximately 400+ words of content</li>
            <li>⚠️ Notice one image missing alt text</li>
            <li>💡 Provide recommendations for improvement</li>
        </ul>
    </div>

    <footer style="margin-top: 50px; padding: 20px; background: #f8f9fa; border-radius: 8px; text-align: center;">
        <p><strong>Test Complete!</strong> If the extension analyzed this page successfully, it's working correctly.</p>
        <p><em>Try testing on other websites to see how it performs on different content types.</em></p>
    </footer>

    <script>
        console.log('Test page loaded - ready for AI SEO Optimizer analysis');
        
        // Add some dynamic content for testing
        document.addEventListener('DOMContentLoaded', function() {
            const timestamp = document.createElement('p');
            timestamp.textContent = 'Page loaded at: ' + new Date().toLocaleString();
            timestamp.style.fontSize = '12px';
            timestamp.style.color = '#666';
            timestamp.style.textAlign = 'center';
            document.querySelector('footer').appendChild(timestamp);
        });
    </script>
</body>
</html>
