// AI SEO Optimizer - Simple Background Service Worker (for testing)

class SEOOptimizerBackground {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupContextMenus();
        console.log('AI SEO Optimizer background script initialized');
    }

    setupEventListeners() {
        // Extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                this.handleInstall();
            }
        });

        // Message handling from popup and content scripts
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });
    }

    setupContextMenus() {
        try {
            if (chrome.contextMenus) {
                chrome.contextMenus.create({
                    id: 'analyze-page',
                    title: 'Analyze page with AI SEO Optimizer',
                    contexts: ['page']
                });
            }
        } catch (error) {
            console.log('Context menus not available:', error);
        }
    }

    handleInstall() {
        console.log('AI SEO Optimizer installed');
        
        // Set default settings
        chrome.storage.sync.set({
            keywordDensity: 2,
            autoAnalysis: false,
            targetKeywords: '',
            analysisHistory: []
        });
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'analyzePage':
                    const analysis = await this.analyzePage(request.data.pageData, request.data.targetKeywords || []);
                    sendResponse({ success: true, data: analysis });
                    break;

                case 'generateKeywords':
                    const keywords = await this.generateKeywords(request.data.pageData);
                    sendResponse({ success: true, data: keywords });
                    break;

                case 'getPageData':
                    const pageData = await this.getPageData(sender.tab.id);
                    sendResponse({ success: true, data: pageData });
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async analyzePage(pageData, targetKeywords = []) {
        // Simple analysis without external modules
        const analysis = {
            seoScore: this.calculateSimpleScore(pageData),
            keywordScore: this.analyzeKeywords(pageData, targetKeywords),
            contentScore: this.analyzeContent(pageData),
            technicalScore: this.analyzeTechnical(pageData),
            recommendations: this.generateSimpleRecommendations(pageData, targetKeywords)
        };

        return analysis;
    }

    calculateSimpleScore(pageData) {
        let score = 0;
        
        // Title analysis
        if (pageData.title && pageData.title.length >= 30 && pageData.title.length <= 60) {
            score += 25;
        }
        
        // Meta description
        if (pageData.metaDescription && pageData.metaDescription.length >= 120 && pageData.metaDescription.length <= 160) {
            score += 25;
        }
        
        // Content length
        if (pageData.content && pageData.content.length > 1000) {
            score += 25;
        }
        
        // Images with alt text
        if (pageData.images && pageData.images.length > 0) {
            const imagesWithAlt = pageData.images.filter(img => img.alt && img.alt.trim().length > 0).length;
            if (imagesWithAlt === pageData.images.length) {
                score += 25;
            }
        } else {
            score += 25; // No images is fine
        }
        
        return Math.min(100, score);
    }

    analyzeKeywords(pageData, targetKeywords) {
        if (!targetKeywords || targetKeywords.length === 0) {
            return 50; // Neutral score if no target keywords
        }

        let score = 0;
        const content = (pageData.content || '').toLowerCase();
        const title = (pageData.title || '').toLowerCase();

        targetKeywords.forEach(keyword => {
            const keywordLower = keyword.toLowerCase();
            
            // Check if keyword is in title
            if (title.includes(keywordLower)) {
                score += 30;
            }
            
            // Check if keyword is in content
            if (content.includes(keywordLower)) {
                score += 20;
            }
        });

        return Math.min(100, score);
    }

    analyzeContent(pageData) {
        let score = 0;
        const content = pageData.content || '';
        const wordCount = content.split(/\s+/).filter(word => word.length > 0).length;

        // Word count scoring
        if (wordCount >= 300) {
            score += 40;
        } else if (wordCount >= 150) {
            score += 20;
        }

        // Heading structure
        if (pageData.headings) {
            if (pageData.headings.h1 && pageData.headings.h1.length === 1) {
                score += 30;
            }
            if (pageData.headings.h2 && pageData.headings.h2.length > 0) {
                score += 30;
            }
        }

        return Math.min(100, score);
    }

    analyzeTechnical(pageData) {
        let score = 0;

        // HTTPS check
        if (pageData.url && pageData.url.startsWith('https://')) {
            score += 50;
        }

        // Meta description exists
        if (pageData.metaDescription && pageData.metaDescription.length > 0) {
            score += 25;
        }

        // Title exists
        if (pageData.title && pageData.title.length > 0) {
            score += 25;
        }

        return Math.min(100, score);
    }

    generateSimpleRecommendations(pageData, targetKeywords) {
        const recommendations = [];

        // Title recommendations
        if (!pageData.title || pageData.title.length < 30) {
            recommendations.push({
                type: 'title',
                title: 'Optimize page title',
                description: 'Page title should be 30-60 characters long',
                priority: 'high'
            });
        }

        // Meta description recommendations
        if (!pageData.metaDescription) {
            recommendations.push({
                type: 'meta',
                title: 'Add meta description',
                description: 'Page is missing a meta description',
                priority: 'high'
            });
        }

        // Content recommendations
        const wordCount = pageData.content ? pageData.content.split(/\s+/).length : 0;
        if (wordCount < 300) {
            recommendations.push({
                type: 'content',
                title: 'Increase content length',
                description: 'Content should be at least 300 words for better SEO',
                priority: 'medium'
            });
        }

        // Image recommendations
        if (pageData.images && pageData.images.length > 0) {
            const imagesWithoutAlt = pageData.images.filter(img => !img.alt || img.alt.trim().length === 0).length;
            if (imagesWithoutAlt > 0) {
                recommendations.push({
                    type: 'image',
                    title: 'Add alt text to images',
                    description: `${imagesWithoutAlt} images are missing alt text`,
                    priority: 'medium'
                });
            }
        }

        return recommendations;
    }

    async generateKeywords(pageData) {
        // Simple keyword extraction
        const content = pageData.content || '';
        const words = content.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 3);

        // Count word frequency
        const wordCount = {};
        words.forEach(word => {
            wordCount[word] = (wordCount[word] || 0) + 1;
        });

        // Get top keywords
        const topWords = Object.entries(wordCount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([word]) => word);

        return {
            primaryKeywords: topWords.slice(0, 5),
            longTailKeywords: [],
            semanticKeywords: topWords.slice(5, 10)
        };
    }

    async getPageData(tabId) {
        const results = await chrome.scripting.executeScript({
            target: { tabId: tabId },
            function: () => {
                return {
                    url: window.location.href,
                    title: document.title,
                    metaDescription: document.querySelector('meta[name="description"]')?.content || '',
                    metaKeywords: document.querySelector('meta[name="keywords"]')?.content || '',
                    headings: {
                        h1: Array.from(document.querySelectorAll('h1')).map(h => ({ text: h.textContent.trim() })),
                        h2: Array.from(document.querySelectorAll('h2')).map(h => ({ text: h.textContent.trim() })),
                        h3: Array.from(document.querySelectorAll('h3')).map(h => ({ text: h.textContent.trim() }))
                    },
                    content: document.body.innerText,
                    wordCount: document.body.innerText.split(/\s+/).length,
                    images: Array.from(document.querySelectorAll('img')).map(img => ({
                        src: img.src,
                        alt: img.alt || '',
                        title: img.title || ''
                    })),
                    links: Array.from(document.querySelectorAll('a')).map(link => ({
                        href: link.href,
                        text: link.textContent.trim(),
                        internal: link.hostname === window.location.hostname
                    }))
                };
            }
        });

        return results[0].result;
    }
}

// Initialize background service worker
new SEOOptimizerBackground();
