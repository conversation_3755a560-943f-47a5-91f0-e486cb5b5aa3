* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 400px;
    min-height: 600px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
}

.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: white;
    border-radius: 8px;
    margin: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 16px;
}

.logo-icon {
    width: 24px;
    height: 24px;
}

.logo h1 {
    font-size: 18px;
    font-weight: 600;
}

.nav-tabs {
    display: flex;
    gap: 4px;
}

.tab-btn {
    flex: 1;
    padding: 8px 12px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.tab-btn.active {
    background: white;
    color: #667eea;
}

.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Dashboard Styles */
.seo-score-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 12px;
    padding: 20px;
    color: white;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.score-circle {
    text-align: center;
    min-width: 80px;
}

.score-number {
    font-size: 32px;
    font-weight: bold;
    line-height: 1;
}

.score-label {
    font-size: 12px;
    opacity: 0.9;
    margin-top: 4px;
}

.score-details {
    flex: 1;
}

.score-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 14px;
}

.score-item:last-child {
    margin-bottom: 0;
}

.quick-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
}

.action-btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.action-btn.primary {
    background: #667eea;
    color: white;
}

.action-btn.primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: #f8f9fa;
    color: #667eea;
    border: 1px solid #e9ecef;
}

.action-btn.secondary:hover {
    background: #e9ecef;
}

.recent-analysis h3 {
    font-size: 16px;
    margin-bottom: 12px;
    color: #333;
}

.recommendations {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.rec-icon {
    font-size: 20px;
    margin-top: 2px;
}

.rec-content {
    flex: 1;
}

.rec-title {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 4px;
    color: #333;
}

.rec-description {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

/* Analysis Styles */
.analysis-section h3 {
    font-size: 18px;
    margin-bottom: 16px;
    color: #333;
}

.analysis-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.analysis-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    border: 1px solid #e9ecef;
}

.analysis-card h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

.analysis-content {
    font-size: 12px;
    color: #666;
}

.loading {
    text-align: center;
    color: #999;
    font-style: italic;
}

/* Settings Styles */
.settings-section {
    margin-bottom: 24px;
}

.settings-section h3 {
    font-size: 16px;
    margin-bottom: 16px;
    color: #333;
}

.setting-group {
    margin-bottom: 16px;
}

.setting-group label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 6px;
    color: #333;
}

.setting-group input,
.setting-group textarea,
.setting-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
    background-color: white;
}

.setting-group input:focus,
.setting-group textarea:focus,
.setting-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-group textarea {
    resize: vertical;
    min-height: 60px;
}

.setting-group small {
    display: block;
    font-size: 12px;
    color: #666;
    margin-top: 4px;
}

.setting-group input[type="range"] {
    margin-bottom: 4px;
}

#density-value {
    font-size: 12px;
    color: #667eea;
    font-weight: 500;
}

/* Checkbox Styles */
.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 6px;
}

.checkbox-label input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
    transform: scale(1.2);
    accent-color: #667eea;
}

.checkbox-label .checkmark {
    margin-left: 4px;
}

.settings-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding-top: 16px;
    border-top: 1px solid #e9ecef;
}

.settings-actions .action-btn {
    flex: 1;
    min-width: 120px;
    font-size: 12px;
    padding: 10px 12px;
}

/* Loading Overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-overlay.hidden {
    display: none;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

.loading-text {
    font-size: 14px;
    color: #666;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Analysis Details Styles */
.analysis-section {
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.analysis-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.analysis-section h5 {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 6px;
    color: #333;
}

.score-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    margin-bottom: 6px;
}

.score-badge.score-good {
    background-color: #d4edda;
    color: #155724;
}

.score-badge.score-medium {
    background-color: #fff3cd;
    color: #856404;
}

.score-badge.score-poor {
    background-color: #f8d7da;
    color: #721c24;
}

.analysis-section p {
    font-size: 11px;
    color: #666;
    margin-bottom: 4px;
}

.issues {
    font-size: 10px;
    color: #dc3545;
    background-color: #f8d7da;
    padding: 4px 6px;
    border-radius: 3px;
    margin-bottom: 4px;
}

.suggestions {
    font-size: 10px;
    color: #155724;
    background-color: #d4edda;
    padding: 4px 6px;
    border-radius: 3px;
    margin-bottom: 4px;
}

.heading-structure {
    font-size: 11px;
    color: #666;
    margin-bottom: 6px;
}

.keyword-stat {
    font-size: 10px;
    margin-bottom: 4px;
    padding: 4px;
    background-color: #f8f9fa;
    border-radius: 3px;
}

.keyword-stat strong {
    color: #333;
}

.keyword-stat .recommendation {
    display: block;
    font-style: italic;
    color: #666;
    margin-top: 2px;
}

.analysis-result {
    font-size: 12px;
    color: #666;
    text-align: center;
    padding: 20px;
    font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 420px) {
    body {
        width: 100vw;
    }

    .container {
        margin: 0;
        border-radius: 0;
        height: 100vh;
    }

    .analysis-grid {
        grid-template-columns: 1fr;
    }

    .analysis-section h5 {
        font-size: 11px;
    }

    .score-badge {
        font-size: 10px;
        padding: 1px 4px;
    }
}
