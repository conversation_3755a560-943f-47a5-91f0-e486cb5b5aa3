// AI SEO Optimizer - Simple Popup Script

console.log('AI SEO Optimizer popup loading...');

class SimplePopup {
    constructor() {
        this.currentTab = 'dashboard';
        this.settings = {};
        this.analysisData = null;
        this.llmProviders = [];
        this.selectedProviderTemplate = null;

        this.init();
    }

    async init() {
        try {
            await this.loadSettings();
            await this.loadLLMProviders();
            this.setupEventListeners();
            this.setupTabNavigation();
            this.updateUI();
            this.renderLLMProviders();
            console.log('Popup initialized successfully');
        } catch (error) {
            console.error('Error initializing popup:', error);
        }
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Dashboard actions
        const analyzeBtn = document.getElementById('analyze-page');
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', () => {
                this.analyzePage();
            });
        }

        const generateBtn = document.getElementById('generate-keywords');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.generateKeywords();
            });
        }

        // Settings
        const saveBtn = document.getElementById('save-settings');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }

        const resetBtn = document.getElementById('reset-settings');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetSettings();
            });
        }

        // Keyword density slider
        const densitySlider = document.getElementById('keyword-density');
        const densityValue = document.getElementById('density-value');
        if (densitySlider && densityValue) {
            densitySlider.addEventListener('input', (e) => {
                densityValue.textContent = e.target.value + '%';
            });
        }

        // LLM Provider management
        const addProviderBtn = document.getElementById('add-llm-provider');
        if (addProviderBtn) {
            addProviderBtn.addEventListener('click', () => {
                this.showAddProviderModal();
            });
        }

        // Modal events
        const closeModal = document.getElementById('close-modal');
        const cancelBtn = document.getElementById('cancel-add-provider');
        const confirmBtn = document.getElementById('confirm-add-provider');

        if (closeModal) {
            closeModal.addEventListener('click', () => {
                this.hideAddProviderModal();
            });
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.hideAddProviderModal();
            });
        }

        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                this.addLLMProvider();
            });
        }

        // Provider template selection
        document.querySelectorAll('.provider-template').forEach(template => {
            template.addEventListener('click', () => {
                this.selectProviderTemplate(template.dataset.provider);
            });
        });
    }

    setupTabNavigation() {
        this.switchTab('dashboard');
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const activeBtn = document.querySelector(`[data-tab="${tabName}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        const activeContent = document.getElementById(tabName);
        if (activeContent) {
            activeContent.classList.add('active');
        }

        this.currentTab = tabName;
    }

    async loadSettings() {
        try {
            // Check if chrome.storage is available (extension context)
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                const result = await chrome.storage.sync.get([
                    'targetKeywords',
                    'keywordDensity',
                    'defaultProvider'
                ]);

                this.settings = {
                    targetKeywords: result.targetKeywords || '',
                    keywordDensity: result.keywordDensity || 2,
                    defaultProvider: result.defaultProvider || ''
                };
            } else {
                // Fallback for testing outside extension context
                console.warn('Chrome storage not available, using localStorage fallback');
                this.settings = {
                    targetKeywords: localStorage.getItem('targetKeywords') || '',
                    keywordDensity: parseInt(localStorage.getItem('keywordDensity')) || 2,
                    defaultProvider: localStorage.getItem('defaultProvider') || ''
                };
            }

            this.updateSettingsUI();
        } catch (error) {
            console.error('Error loading settings:', error);
            // Fallback to default settings
            this.settings = {
                targetKeywords: '',
                keywordDensity: 2,
                defaultProvider: ''
            };
        }
    }

    async loadLLMProviders() {
        try {
            let providers = [];

            // Check if chrome.storage is available (extension context)
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                const result = await chrome.storage.sync.get(['llmProviders']);
                providers = result.llmProviders || [];
            } else {
                // Fallback for testing outside extension context
                console.warn('Chrome storage not available, using localStorage fallback');
                const stored = localStorage.getItem('llmProviders');
                providers = stored ? JSON.parse(stored) : [];
            }

            this.llmProviders = providers;

            // Add default providers if none exist
            if (this.llmProviders.length === 0) {
                this.llmProviders = [
                    {
                        id: 'openai-default',
                        name: 'OpenAI',
                        type: 'openai',
                        apiKey: '',
                        baseUrl: 'https://api.openai.com/v1',
                        model: 'gpt-3.5-turbo',
                        maxTokens: 4000,
                        enabled: true,
                        isDefault: false
                    }
                ];
                await this.saveLLMProviders();
            }
        } catch (error) {
            console.error('Error loading LLM providers:', error);
            this.llmProviders = [];
        }
    }

    updateSettingsUI() {
        const elements = {
            'target-keywords': this.settings.targetKeywords,
            'keyword-density': this.settings.keywordDensity,
            'default-provider': this.settings.defaultProvider
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.value = value;
            }
        });

        const densityValue = document.getElementById('density-value');
        if (densityValue) {
            densityValue.textContent = this.settings.keywordDensity + '%';
        }
    }

    async saveSettings() {
        try {
            const settings = {
                targetKeywords: this.getElementValue('target-keywords'),
                keywordDensity: parseFloat(this.getElementValue('keyword-density')),
                defaultProvider: this.getElementValue('default-provider')
            };

            // Check if chrome.storage is available (extension context)
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                await chrome.storage.sync.set(settings);
            } else {
                // Fallback for testing outside extension context
                console.warn('Chrome storage not available, using localStorage fallback');
                Object.entries(settings).forEach(([key, value]) => {
                    localStorage.setItem(key, value);
                });
            }

            this.settings = settings;
            this.showNotification('Settings saved successfully!', 'success');
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showNotification('Error saving settings', 'error');
        }
    }

    async resetSettings() {
        try {
            // Check if chrome.storage is available (extension context)
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                await chrome.storage.sync.clear();
            } else {
                // Fallback for testing outside extension context
                console.warn('Chrome storage not available, using localStorage fallback');
                localStorage.clear();
            }

            this.settings = {
                targetKeywords: '',
                keywordDensity: 2,
                defaultProvider: ''
            };

            this.llmProviders = [];
            await this.loadLLMProviders(); // This will recreate default providers

            this.updateSettingsUI();
            this.renderLLMProviders();
            this.showNotification('Settings reset to default', 'success');
        } catch (error) {
            console.error('Error resetting settings:', error);
            this.showNotification('Error resetting settings', 'error');
        }
    }

    async analyzePage() {
        this.showLoading('Analyzing page...');

        try {
            // Check if we're in extension context
            if (typeof chrome !== 'undefined' && chrome.tabs && chrome.runtime) {
                try {
                    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
                    const pageData = await this.getPageData(tab?.id);

                    const response = await chrome.runtime.sendMessage({
                        action: 'analyzePage',
                        data: {
                            pageData: pageData,
                            targetKeywords: this.settings.targetKeywords.split(',').map(k => k.trim()).filter(k => k)
                        }
                    });

                    if (response && response.success) {
                        this.analysisData = response.data;
                        this.updateDashboard(response.data);
                        this.updateAnalysisTab(response.data);
                    } else {
                        throw new Error(response?.error || 'Analysis failed');
                    }
                } catch (chromeError) {
                    console.warn('Chrome API error, using mock analysis:', chromeError);
                    const mockAnalysis = this.getMockAnalysis();
                    this.analysisData = mockAnalysis;
                    this.updateDashboard(mockAnalysis);
                    this.updateAnalysisTab(mockAnalysis);
                }
            } else {
                // Fallback for testing outside extension context
                console.warn('Chrome APIs not available, using mock analysis');
                const mockAnalysis = this.getMockAnalysis();
                this.analysisData = mockAnalysis;
                this.updateDashboard(mockAnalysis);
                this.updateAnalysisTab(mockAnalysis);
            }

        } catch (error) {
            console.error('Analysis error:', error);
            this.showNotification('Error analyzing page: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async generateKeywords() {
        this.showLoading('Generating keywords...');

        try {
            // Check if we're in extension context
            if (typeof chrome !== 'undefined' && chrome.tabs && chrome.runtime) {
                const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

                const response = await chrome.runtime.sendMessage({
                    action: 'generateKeywords',
                    data: { pageData: await this.getPageData(tab.id) }
                });

                if (response.success) {
                    const keywords = [
                        ...response.data.primaryKeywords,
                        ...response.data.semanticKeywords
                    ];

                    const keywordsElement = document.getElementById('target-keywords');
                    if (keywordsElement) {
                        keywordsElement.value = keywords.join(', ');
                    }

                    this.showNotification('Keywords generated successfully!', 'success');
                } else {
                    throw new Error(response.error || 'Keyword generation failed');
                }
            } else {
                // Fallback for testing outside extension context
                console.warn('Chrome APIs not available, using mock keywords');
                const mockKeywords = ['seo', 'optimization', 'content', 'keywords', 'analysis'];

                const keywordsElement = document.getElementById('target-keywords');
                if (keywordsElement) {
                    keywordsElement.value = mockKeywords.join(', ');
                }

                this.showNotification('Mock keywords generated for testing!', 'success');
            }

        } catch (error) {
            console.error('Keyword generation error:', error);
            this.showNotification('Error generating keywords: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async getPageData(tabId) {
        // Check if we're in extension context
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            const response = await chrome.runtime.sendMessage({
                action: 'getPageData'
            });

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.error || 'Failed to get page data');
            }
        } else {
            // Fallback for testing outside extension context
            return this.getMockPageData();
        }
    }

    updateDashboard(analysis) {
        this.setElementText('seo-score', analysis.seoScore || '--');

        // Update score details with styling
        const keywordClass = this.getScoreClass(analysis.keywordScore);
        const contentClass = this.getScoreClass(analysis.contentScore);
        const technicalClass = this.getScoreClass(analysis.technicalScore);

        this.setElementHTML('keyword-score', `<span class="score-value ${keywordClass}">${analysis.keywordScore || '--'}</span>`);
        this.setElementHTML('content-score', `<span class="score-value ${contentClass}">${analysis.contentScore || '--'}</span>`);
        this.setElementHTML('technical-score', `<span class="score-value ${technicalClass}">${analysis.technicalScore || '--'}</span>`);

        // Update recommendations
        const recommendationsList = document.getElementById('recommendations-list');
        if (recommendationsList && analysis.recommendations) {
            recommendationsList.innerHTML = '';

            analysis.recommendations.forEach(rec => {
                const recElement = document.createElement('div');
                recElement.className = 'recommendation-item';
                recElement.innerHTML = `
                    <div class="rec-icon">${this.getRecommendationIcon(rec.type)}</div>
                    <div class="rec-content">
                        <div class="rec-title">${rec.title}</div>
                        <div class="rec-description">${rec.description}</div>
                    </div>
                `;
                recommendationsList.appendChild(recElement);
            });
        }
    }

    getScoreClass(score) {
        if (score >= 80) return 'good';
        if (score >= 60) return 'medium';
        return 'poor';
    }

    updateAnalysisTab(analysis) {
        // Meta Tags Analysis
        const metaHtml = `
            <div class="analysis-section">
                <h5>Title Tag</h5>
                <div class="score-badge ${analysis.seoScore > 70 ? 'score-good' : 'score-medium'}">
                    Score: ${analysis.seoScore}/100
                </div>
                <div class="analysis-details">
                    <div class="detail-item">Length: Good (30-60 characters recommended)</div>
                    <div class="suggestions">💡 Consider including target keywords in title</div>
                </div>
            </div>
            <div class="analysis-section">
                <h5>Meta Description</h5>
                <div class="score-badge ${analysis.contentScore > 70 ? 'score-good' : 'score-medium'}">
                    Score: ${analysis.contentScore}/100
                </div>
                <div class="analysis-details">
                    <div class="detail-item">Length: Needs improvement (120-160 characters recommended)</div>
                    <div class="suggestions">💡 Make description more compelling and keyword-rich</div>
                </div>
            </div>
        `;

        // Headings Structure Analysis
        const headingsHtml = `
            <div class="analysis-section">
                <h5>Heading Structure</h5>
                <div class="score-badge ${analysis.technicalScore > 70 ? 'score-good' : 'score-medium'}">
                    Score: ${analysis.technicalScore}/100
                </div>
                <div class="heading-structure">
                    <div class="detail-item">✅ H1: Found (1 instance)</div>
                    <div class="detail-item">✅ H2: Found (2 instances)</div>
                    <div class="detail-item">✅ H3: Found (2 instances)</div>
                </div>
                <div class="suggestions">💡 Good heading hierarchy structure</div>
            </div>
        `;

        // Keyword Analysis
        const keywordHtml = `
            <div class="analysis-section">
                <h5>Keyword Density</h5>
                <div class="score-badge ${analysis.keywordScore > 60 ? 'score-good' : 'score-medium'}">
                    Score: ${analysis.keywordScore}/100
                </div>
                <div class="keyword-stat">
                    <strong>Target Keywords:</strong> seo, optimization, ai
                    <span class="recommendation">Density: 2.1% (Optimal: 1-3%)</span>
                </div>
                <div class="keyword-stat">
                    <strong>Related Keywords:</strong> content, analysis, chrome
                    <span class="recommendation">Consider adding more semantic keywords</span>
                </div>
            </div>
        `;

        // Content Quality Analysis
        const contentHtml = `
            <div class="analysis-section">
                <h5>Content Quality</h5>
                <div class="score-badge ${analysis.contentScore > 70 ? 'score-good' : 'score-medium'}">
                    Score: ${analysis.contentScore}/100
                </div>
                <div class="detail-item">📝 Word Count: 450+ words (Good)</div>
                <div class="detail-item">🖼️ Images: 2 found (1 missing alt text)</div>
                <div class="detail-item">🔗 Links: 2 found (1 internal, 1 external)</div>
                <div class="suggestions">💡 Content length is good, focus on keyword optimization</div>
            </div>
        `;

        this.setElementHTML('meta-analysis', metaHtml);
        this.setElementHTML('headings-analysis', headingsHtml);
        this.setElementHTML('keyword-analysis', keywordHtml);
        this.setElementHTML('content-analysis', contentHtml);
    }

    getRecommendationIcon(type) {
        const icons = {
            title: '📝',
            meta: '🏷️',
            heading: '📋',
            content: '📄',
            image: '🖼️',
            link: '🔗',
            keyword: '🎯',
            technical: '⚙️'
        };
        return icons[type] || '💡';
    }

    // LLM Provider Management Methods
    async saveLLMProviders() {
        try {
            // Check if chrome.storage is available (extension context)
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.sync) {
                await chrome.storage.sync.set({ llmProviders: this.llmProviders });
            } else {
                // Fallback for testing outside extension context
                console.warn('Chrome storage not available, using localStorage fallback');
                localStorage.setItem('llmProviders', JSON.stringify(this.llmProviders));
            }
        } catch (error) {
            console.error('Error saving LLM providers:', error);
        }
    }

    renderLLMProviders() {
        const container = document.getElementById('llm-providers-list');
        const defaultSelect = document.getElementById('default-provider');

        if (!container || !defaultSelect) return;

        // Clear existing content
        container.innerHTML = '';
        defaultSelect.innerHTML = '<option value="">Select a provider...</option>';

        this.llmProviders.forEach(provider => {
            // Add to providers list
            const providerElement = this.createProviderElement(provider);
            container.appendChild(providerElement);

            // Add to default provider dropdown
            const option = document.createElement('option');
            option.value = provider.id;
            option.textContent = provider.name;
            if (provider.id === this.settings.defaultProvider) {
                option.selected = true;
            }
            defaultSelect.appendChild(option);
        });
    }

    createProviderElement(provider) {
        const element = document.createElement('div');
        element.className = `llm-provider-item ${provider.isDefault ? 'active' : ''}`;
        element.innerHTML = `
            <div class="provider-header">
                <div class="provider-name">
                    ${this.getProviderIcon(provider.type)} ${provider.name}
                    <span class="provider-status ${provider.apiKey ? 'connected' : 'disconnected'}">
                        ${provider.apiKey ? 'Connected' : 'Not Configured'}
                    </span>
                </div>
                <div class="provider-actions">
                    <button type="button" class="btn-icon edit" data-provider-id="${provider.id}" title="Edit">
                        ✏️
                    </button>
                    <button type="button" class="btn-icon delete" data-provider-id="${provider.id}" title="Delete">
                        🗑️
                    </button>
                </div>
            </div>
            <div class="provider-config">
                <div class="config-row">
                    <label>Type:</label>
                    <span>${provider.type}</span>
                </div>
                <div class="config-row">
                    <label>Model:</label>
                    <span>${provider.model}</span>
                </div>
                <div class="config-row">
                    <label>API Key:</label>
                    <input type="password" value="${provider.apiKey}"
                           data-provider-id="${provider.id}"
                           data-field="apiKey"
                           placeholder="Enter API key">
                </div>
                ${provider.type === 'custom' ? `
                <div class="config-row">
                    <label>Base URL:</label>
                    <input type="url" value="${provider.baseUrl}"
                           data-provider-id="${provider.id}"
                           data-field="baseUrl"
                           placeholder="https://api.example.com/v1">
                </div>
                ` : ''}
            </div>
        `;

        // Add event listeners
        const editBtn = element.querySelector('.edit');
        const deleteBtn = element.querySelector('.delete');
        const inputs = element.querySelectorAll('input');

        if (editBtn) {
            editBtn.addEventListener('click', () => {
                this.editProvider(provider.id);
            });
        }

        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => {
                this.deleteProvider(provider.id);
            });
        }

        inputs.forEach(input => {
            input.addEventListener('change', (e) => {
                this.updateProviderField(e.target.dataset.providerId, e.target.dataset.field, e.target.value);
            });
        });

        return element;
    }

    getProviderIcon(type) {
        const icons = {
            openai: '🤖',
            anthropic: '🧠',
            gemini: '💎',
            openrouter: '🌐',
            ollama: '🏠',
            groq: '⚡',
            custom: '⚙️'
        };
        return icons[type] || '🔧';
    }

    showAddProviderModal() {
        const modal = document.getElementById('add-provider-modal');
        if (modal) {
            modal.classList.remove('hidden');
            this.selectedProviderTemplate = null;
            this.updateAddProviderForm();
        }
    }

    hideAddProviderModal() {
        const modal = document.getElementById('add-provider-modal');
        if (modal) {
            modal.classList.add('hidden');
            this.resetAddProviderForm();
        }
    }

    selectProviderTemplate(providerType) {
        // Update template selection
        document.querySelectorAll('.provider-template').forEach(template => {
            template.classList.remove('selected');
        });

        const selectedTemplate = document.querySelector(`[data-provider="${providerType}"]`);
        if (selectedTemplate) {
            selectedTemplate.classList.add('selected');
        }

        this.selectedProviderTemplate = providerType;
        this.updateAddProviderForm();
    }

    updateAddProviderForm() {
        const form = document.getElementById('provider-form');
        const confirmBtn = document.getElementById('confirm-add-provider');

        if (!form || !confirmBtn) return;

        if (this.selectedProviderTemplate) {
            form.classList.remove('hidden');
            confirmBtn.disabled = false;

            // Pre-fill form based on template
            const templates = {
                openai: {
                    name: 'OpenAI',
                    baseUrl: 'https://api.openai.com/v1',
                    model: 'gpt-3.5-turbo',
                    maxTokens: 4000
                },
                anthropic: {
                    name: 'Anthropic',
                    baseUrl: 'https://api.anthropic.com/v1',
                    model: 'claude-3-sonnet-20240229',
                    maxTokens: 4000
                },
                gemini: {
                    name: 'Google Gemini',
                    baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
                    model: 'gemini-pro',
                    maxTokens: 4000
                },
                openrouter: {
                    name: 'OpenRouter',
                    baseUrl: 'https://openrouter.ai/api/v1',
                    model: 'openai/gpt-3.5-turbo',
                    maxTokens: 4000
                },
                ollama: {
                    name: 'Ollama',
                    baseUrl: 'http://localhost:11434/v1',
                    model: 'llama2',
                    maxTokens: 2000
                },
                groq: {
                    name: 'Groq',
                    baseUrl: 'https://api.groq.com/openai/v1',
                    model: 'llama2-70b-4096',
                    maxTokens: 4000
                },
                custom: {
                    name: 'Custom Provider',
                    baseUrl: '',
                    model: '',
                    maxTokens: 4000
                }
            };

            const template = templates[this.selectedProviderTemplate];
            if (template) {
                document.getElementById('provider-name').value = template.name;
                document.getElementById('provider-base-url').value = template.baseUrl;
                document.getElementById('provider-model').value = template.model;
                document.getElementById('provider-max-tokens').value = template.maxTokens;
            }
        } else {
            form.classList.add('hidden');
            confirmBtn.disabled = true;
        }
    }

    async addLLMProvider() {
        try {
            const newProvider = {
                id: 'provider-' + Date.now(),
                name: this.getElementValue('provider-name'),
                type: this.selectedProviderTemplate,
                apiKey: this.getElementValue('provider-api-key'),
                baseUrl: this.getElementValue('provider-base-url'),
                model: this.getElementValue('provider-model'),
                maxTokens: parseInt(this.getElementValue('provider-max-tokens')),
                enabled: true,
                isDefault: this.llmProviders.length === 0
            };

            this.llmProviders.push(newProvider);
            await this.saveLLMProviders();
            this.renderLLMProviders();
            this.hideAddProviderModal();

            this.showNotification(`${newProvider.name} provider added successfully!`, 'success');
        } catch (error) {
            console.error('Error adding provider:', error);
            this.showNotification('Error adding provider', 'error');
        }
    }

    async deleteProvider(providerId) {
        if (confirm('Are you sure you want to delete this provider?')) {
            try {
                this.llmProviders = this.llmProviders.filter(p => p.id !== providerId);
                await this.saveLLMProviders();
                this.renderLLMProviders();

                this.showNotification('Provider deleted successfully!', 'success');
            } catch (error) {
                console.error('Error deleting provider:', error);
                this.showNotification('Error deleting provider', 'error');
            }
        }
    }

    async updateProviderField(providerId, field, value) {
        try {
            const provider = this.llmProviders.find(p => p.id === providerId);
            if (provider) {
                provider[field] = value;
                await this.saveLLMProviders();

                // Update UI to show connection status
                setTimeout(() => {
                    this.renderLLMProviders();
                }, 100);
            }
        } catch (error) {
            console.error('Error updating provider:', error);
        }
    }

    editProvider(providerId) {
        // For now, just focus on the API key field
        const providerElement = document.querySelector(`[data-provider-id="${providerId}"]`);
        if (providerElement) {
            const apiKeyInput = providerElement.closest('.llm-provider-item').querySelector('input[data-field="apiKey"]');
            if (apiKeyInput) {
                apiKeyInput.focus();
            }
        }
    }

    resetAddProviderForm() {
        document.getElementById('provider-name').value = '';
        document.getElementById('provider-api-key').value = '';
        document.getElementById('provider-base-url').value = '';
        document.getElementById('provider-model').value = '';
        document.getElementById('provider-max-tokens').value = '4000';

        document.querySelectorAll('.provider-template').forEach(template => {
            template.classList.remove('selected');
        });

        const form = document.getElementById('provider-form');
        if (form) {
            form.classList.add('hidden');
        }
    }

    // Mock data for testing outside extension context
    getMockPageData() {
        return {
            url: window.location.href,
            title: document.title || 'AI SEO Optimizer Test Page',
            metaDescription: document.querySelector('meta[name="description"]')?.content || 'Test page for AI SEO Optimizer extension',
            metaKeywords: 'seo, optimization, ai, chrome extension',
            headings: {
                h1: [{ text: 'AI SEO Optimizer Test Page' }],
                h2: [{ text: 'Content for SEO Testing' }, { text: 'Expected Results' }],
                h3: [{ text: 'SEO Best Practices Example' }, { text: 'Common SEO Issues' }]
            },
            content: document.body ? document.body.innerText : 'This is test content for the AI SEO Optimizer extension. It includes various SEO elements to test the analysis capabilities.',
            wordCount: document.body ? document.body.innerText.split(/\s+/).filter(w => w.length > 0).length : 450,
            images: [
                { src: 'test-image.jpg', alt: 'AI SEO Optimizer test image with proper alt text', title: 'Test Image' },
                { src: 'bad-image.jpg', alt: '', title: '' }
            ],
            links: [
                { href: '#internal-link', text: 'Internal link', internal: true },
                { href: 'https://example.com', text: 'External link', internal: false }
            ]
        };
    }

    getMockAnalysis() {
        return {
            seoScore: 78,
            keywordScore: 65,
            contentScore: 82,
            technicalScore: 74,
            recommendations: [
                {
                    type: 'title',
                    title: 'Optimize Title Tag',
                    description: 'Consider including more target keywords in your title tag'
                },
                {
                    type: 'meta',
                    title: 'Improve Meta Description',
                    description: 'Meta description could be more compelling and include target keywords'
                },
                {
                    type: 'image',
                    title: 'Add Alt Text to Images',
                    description: '1 image is missing alt text for better accessibility'
                },
                {
                    type: 'content',
                    title: 'Content Optimization',
                    description: 'Consider adding more relevant keywords throughout your content'
                },
                {
                    type: 'keyword',
                    title: 'Keyword Density',
                    description: 'Target keyword density is optimal at 2.1%'
                },
                {
                    type: 'technical',
                    title: 'Page Speed',
                    description: 'Consider optimizing images and minifying CSS/JS files'
                }
            ]
        };
    }

    // Utility methods
    getElementValue(id) {
        const element = document.getElementById(id);
        return element ? element.value : '';
    }

    setElementText(id, text) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = text;
        }
    }

    setElementHTML(id, html) {
        const element = document.getElementById(id);
        if (element) {
            element.innerHTML = html;
        }
    }

    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            const text = overlay.querySelector('.loading-text');
            if (text) {
                text.textContent = message;
            }
            overlay.classList.remove('hidden');
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    showNotification(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        alert(message); // Simple notification for now
    }

    updateUI() {
        // Update UI based on current state
        console.log('UI updated');
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing popup...');
    new SimplePopup();
});

console.log('Popup script loaded');
