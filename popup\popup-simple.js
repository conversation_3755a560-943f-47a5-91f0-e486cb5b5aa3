// AI SEO Optimizer - Simple Popup Script

console.log('AI SEO Optimizer popup loading...');

class SimplePopup {
    constructor() {
        this.currentTab = 'dashboard';
        this.settings = {};
        this.analysisData = null;
        this.llmProviders = [];
        this.selectedProviderTemplate = null;

        this.init();
    }

    async init() {
        try {
            await this.loadSettings();
            await this.loadLLMProviders();
            this.setupEventListeners();
            this.setupTabNavigation();
            this.updateUI();
            this.renderLLMProviders();
            console.log('Popup initialized successfully');
        } catch (error) {
            console.error('Error initializing popup:', error);
        }
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Dashboard actions
        const analyzeBtn = document.getElementById('analyze-page');
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', () => {
                this.analyzePage();
            });
        }

        const generateBtn = document.getElementById('generate-keywords');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.generateKeywords();
            });
        }

        // Settings
        const saveBtn = document.getElementById('save-settings');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }

        const resetBtn = document.getElementById('reset-settings');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetSettings();
            });
        }

        // Keyword density slider
        const densitySlider = document.getElementById('keyword-density');
        const densityValue = document.getElementById('density-value');
        if (densitySlider && densityValue) {
            densitySlider.addEventListener('input', (e) => {
                densityValue.textContent = e.target.value + '%';
            });
        }

        // LLM Provider management
        const addProviderBtn = document.getElementById('add-llm-provider');
        if (addProviderBtn) {
            addProviderBtn.addEventListener('click', () => {
                this.showAddProviderModal();
            });
        }

        // Modal events
        const closeModal = document.getElementById('close-modal');
        const cancelBtn = document.getElementById('cancel-add-provider');
        const confirmBtn = document.getElementById('confirm-add-provider');

        if (closeModal) {
            closeModal.addEventListener('click', () => {
                this.hideAddProviderModal();
            });
        }

        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                this.hideAddProviderModal();
            });
        }

        if (confirmBtn) {
            confirmBtn.addEventListener('click', () => {
                this.addLLMProvider();
            });
        }

        // Provider template selection
        document.querySelectorAll('.provider-template').forEach(template => {
            template.addEventListener('click', () => {
                this.selectProviderTemplate(template.dataset.provider);
            });
        });
    }

    setupTabNavigation() {
        this.switchTab('dashboard');
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const activeBtn = document.querySelector(`[data-tab="${tabName}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        const activeContent = document.getElementById(tabName);
        if (activeContent) {
            activeContent.classList.add('active');
        }

        this.currentTab = tabName;
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get([
                'targetKeywords',
                'keywordDensity',
                'defaultProvider'
            ]);

            this.settings = {
                targetKeywords: result.targetKeywords || '',
                keywordDensity: result.keywordDensity || 2,
                defaultProvider: result.defaultProvider || ''
            };

            this.updateSettingsUI();
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    async loadLLMProviders() {
        try {
            const result = await chrome.storage.sync.get(['llmProviders']);
            this.llmProviders = result.llmProviders || [];

            // Add default providers if none exist
            if (this.llmProviders.length === 0) {
                this.llmProviders = [
                    {
                        id: 'openai-default',
                        name: 'OpenAI',
                        type: 'openai',
                        apiKey: '',
                        baseUrl: 'https://api.openai.com/v1',
                        model: 'gpt-3.5-turbo',
                        maxTokens: 4000,
                        enabled: true,
                        isDefault: false
                    }
                ];
                await this.saveLLMProviders();
            }
        } catch (error) {
            console.error('Error loading LLM providers:', error);
            this.llmProviders = [];
        }
    }

    updateSettingsUI() {
        const elements = {
            'target-keywords': this.settings.targetKeywords,
            'keyword-density': this.settings.keywordDensity,
            'default-provider': this.settings.defaultProvider
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.value = value;
            }
        });

        const densityValue = document.getElementById('density-value');
        if (densityValue) {
            densityValue.textContent = this.settings.keywordDensity + '%';
        }
    }

    async saveSettings() {
        try {
            const settings = {
                targetKeywords: this.getElementValue('target-keywords'),
                keywordDensity: parseFloat(this.getElementValue('keyword-density')),
                defaultProvider: this.getElementValue('default-provider')
            };

            await chrome.storage.sync.set(settings);
            this.settings = settings;

            this.showNotification('Settings saved successfully!', 'success');
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showNotification('Error saving settings', 'error');
        }
    }

    async resetSettings() {
        try {
            await chrome.storage.sync.clear();

            this.settings = {
                targetKeywords: '',
                keywordDensity: 2,
                defaultProvider: ''
            };

            this.llmProviders = [];
            await this.loadLLMProviders(); // This will recreate default providers

            this.updateSettingsUI();
            this.renderLLMProviders();
            this.showNotification('Settings reset to default', 'success');
        } catch (error) {
            console.error('Error resetting settings:', error);
            this.showNotification('Error resetting settings', 'error');
        }
    }

    async analyzePage() {
        this.showLoading('Analyzing page...');

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const response = await chrome.runtime.sendMessage({
                action: 'analyzePage',
                data: {
                    pageData: await this.getPageData(tab.id),
                    targetKeywords: this.settings.targetKeywords.split(',').map(k => k.trim()).filter(k => k)
                }
            });

            if (response.success) {
                this.analysisData = response.data;
                this.updateDashboard(response.data);
                this.updateAnalysisTab(response.data);
            } else {
                throw new Error(response.error || 'Analysis failed');
            }

        } catch (error) {
            console.error('Analysis error:', error);
            this.showNotification('Error analyzing page: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async generateKeywords() {
        this.showLoading('Generating keywords...');

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const response = await chrome.runtime.sendMessage({
                action: 'generateKeywords',
                data: { pageData: await this.getPageData(tab.id) }
            });

            if (response.success) {
                const keywords = [
                    ...response.data.primaryKeywords,
                    ...response.data.semanticKeywords
                ];
                
                const keywordsElement = document.getElementById('target-keywords');
                if (keywordsElement) {
                    keywordsElement.value = keywords.join(', ');
                }
                
                this.showNotification('Keywords generated successfully!', 'success');
            } else {
                throw new Error(response.error || 'Keyword generation failed');
            }

        } catch (error) {
            console.error('Keyword generation error:', error);
            this.showNotification('Error generating keywords: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async getPageData(tabId) {
        const response = await chrome.runtime.sendMessage({
            action: 'getPageData'
        });
        
        if (response.success) {
            return response.data;
        } else {
            throw new Error(response.error || 'Failed to get page data');
        }
    }

    updateDashboard(analysis) {
        this.setElementText('seo-score', analysis.seoScore || '--');
        this.setElementText('keyword-score', analysis.keywordScore || '--');
        this.setElementText('content-score', analysis.contentScore || '--');
        this.setElementText('technical-score', analysis.technicalScore || '--');

        // Update recommendations
        const recommendationsList = document.getElementById('recommendations-list');
        if (recommendationsList && analysis.recommendations) {
            recommendationsList.innerHTML = '';

            analysis.recommendations.forEach(rec => {
                const recElement = document.createElement('div');
                recElement.className = 'recommendation-item';
                recElement.innerHTML = `
                    <div class="rec-icon">${this.getRecommendationIcon(rec.type)}</div>
                    <div class="rec-content">
                        <div class="rec-title">${rec.title}</div>
                        <div class="rec-description">${rec.description}</div>
                    </div>
                `;
                recommendationsList.appendChild(recElement);
            });
        }
    }

    updateAnalysisTab(analysis) {
        // Simple analysis display
        this.setElementHTML('meta-analysis', '<div class="analysis-result">Analysis complete - check dashboard for results</div>');
        this.setElementHTML('headings-analysis', '<div class="analysis-result">Analysis complete - check dashboard for results</div>');
        this.setElementHTML('keyword-analysis', '<div class="analysis-result">Analysis complete - check dashboard for results</div>');
        this.setElementHTML('content-analysis', '<div class="analysis-result">Analysis complete - check dashboard for results</div>');
    }

    getRecommendationIcon(type) {
        const icons = {
            title: '📝',
            meta: '🏷️',
            heading: '📋',
            content: '📄',
            image: '🖼️',
            link: '🔗',
            keyword: '🎯',
            technical: '⚙️'
        };
        return icons[type] || '💡';
    }

    // LLM Provider Management Methods
    async saveLLMProviders() {
        try {
            await chrome.storage.sync.set({ llmProviders: this.llmProviders });
        } catch (error) {
            console.error('Error saving LLM providers:', error);
        }
    }

    renderLLMProviders() {
        const container = document.getElementById('llm-providers-list');
        const defaultSelect = document.getElementById('default-provider');

        if (!container || !defaultSelect) return;

        // Clear existing content
        container.innerHTML = '';
        defaultSelect.innerHTML = '<option value="">Select a provider...</option>';

        this.llmProviders.forEach(provider => {
            // Add to providers list
            const providerElement = this.createProviderElement(provider);
            container.appendChild(providerElement);

            // Add to default provider dropdown
            const option = document.createElement('option');
            option.value = provider.id;
            option.textContent = provider.name;
            if (provider.id === this.settings.defaultProvider) {
                option.selected = true;
            }
            defaultSelect.appendChild(option);
        });
    }

    createProviderElement(provider) {
        const element = document.createElement('div');
        element.className = `llm-provider-item ${provider.isDefault ? 'active' : ''}`;
        element.innerHTML = `
            <div class="provider-header">
                <div class="provider-name">
                    ${this.getProviderIcon(provider.type)} ${provider.name}
                    <span class="provider-status ${provider.apiKey ? 'connected' : 'disconnected'}">
                        ${provider.apiKey ? 'Connected' : 'Not Configured'}
                    </span>
                </div>
                <div class="provider-actions">
                    <button type="button" class="btn-icon edit" data-provider-id="${provider.id}" title="Edit">
                        ✏️
                    </button>
                    <button type="button" class="btn-icon delete" data-provider-id="${provider.id}" title="Delete">
                        🗑️
                    </button>
                </div>
            </div>
            <div class="provider-config">
                <div class="config-row">
                    <label>Type:</label>
                    <span>${provider.type}</span>
                </div>
                <div class="config-row">
                    <label>Model:</label>
                    <span>${provider.model}</span>
                </div>
                <div class="config-row">
                    <label>API Key:</label>
                    <input type="password" value="${provider.apiKey}"
                           data-provider-id="${provider.id}"
                           data-field="apiKey"
                           placeholder="Enter API key">
                </div>
                ${provider.type === 'custom' ? `
                <div class="config-row">
                    <label>Base URL:</label>
                    <input type="url" value="${provider.baseUrl}"
                           data-provider-id="${provider.id}"
                           data-field="baseUrl"
                           placeholder="https://api.example.com/v1">
                </div>
                ` : ''}
            </div>
        `;

        // Add event listeners
        const editBtn = element.querySelector('.edit');
        const deleteBtn = element.querySelector('.delete');
        const inputs = element.querySelectorAll('input');

        if (editBtn) {
            editBtn.addEventListener('click', () => {
                this.editProvider(provider.id);
            });
        }

        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => {
                this.deleteProvider(provider.id);
            });
        }

        inputs.forEach(input => {
            input.addEventListener('change', (e) => {
                this.updateProviderField(e.target.dataset.providerId, e.target.dataset.field, e.target.value);
            });
        });

        return element;
    }

    getProviderIcon(type) {
        const icons = {
            openai: '🤖',
            anthropic: '🧠',
            openrouter: '🌐',
            ollama: '🏠',
            groq: '⚡',
            custom: '⚙️'
        };
        return icons[type] || '🔧';
    }

    showAddProviderModal() {
        const modal = document.getElementById('add-provider-modal');
        if (modal) {
            modal.classList.remove('hidden');
            this.selectedProviderTemplate = null;
            this.updateAddProviderForm();
        }
    }

    hideAddProviderModal() {
        const modal = document.getElementById('add-provider-modal');
        if (modal) {
            modal.classList.add('hidden');
            this.resetAddProviderForm();
        }
    }

    selectProviderTemplate(providerType) {
        // Update template selection
        document.querySelectorAll('.provider-template').forEach(template => {
            template.classList.remove('selected');
        });

        const selectedTemplate = document.querySelector(`[data-provider="${providerType}"]`);
        if (selectedTemplate) {
            selectedTemplate.classList.add('selected');
        }

        this.selectedProviderTemplate = providerType;
        this.updateAddProviderForm();
    }

    updateAddProviderForm() {
        const form = document.getElementById('provider-form');
        const confirmBtn = document.getElementById('confirm-add-provider');

        if (!form || !confirmBtn) return;

        if (this.selectedProviderTemplate) {
            form.classList.remove('hidden');
            confirmBtn.disabled = false;

            // Pre-fill form based on template
            const templates = {
                openai: {
                    name: 'OpenAI',
                    baseUrl: 'https://api.openai.com/v1',
                    model: 'gpt-3.5-turbo',
                    maxTokens: 4000
                },
                anthropic: {
                    name: 'Anthropic',
                    baseUrl: 'https://api.anthropic.com/v1',
                    model: 'claude-3-sonnet-20240229',
                    maxTokens: 4000
                },
                openrouter: {
                    name: 'OpenRouter',
                    baseUrl: 'https://openrouter.ai/api/v1',
                    model: 'openai/gpt-3.5-turbo',
                    maxTokens: 4000
                },
                ollama: {
                    name: 'Ollama',
                    baseUrl: 'http://localhost:11434/v1',
                    model: 'llama2',
                    maxTokens: 2000
                },
                groq: {
                    name: 'Groq',
                    baseUrl: 'https://api.groq.com/openai/v1',
                    model: 'llama2-70b-4096',
                    maxTokens: 4000
                },
                custom: {
                    name: 'Custom Provider',
                    baseUrl: '',
                    model: '',
                    maxTokens: 4000
                }
            };

            const template = templates[this.selectedProviderTemplate];
            if (template) {
                document.getElementById('provider-name').value = template.name;
                document.getElementById('provider-base-url').value = template.baseUrl;
                document.getElementById('provider-model').value = template.model;
                document.getElementById('provider-max-tokens').value = template.maxTokens;
            }
        } else {
            form.classList.add('hidden');
            confirmBtn.disabled = true;
        }
    }

    async addLLMProvider() {
        try {
            const newProvider = {
                id: 'provider-' + Date.now(),
                name: this.getElementValue('provider-name'),
                type: this.selectedProviderTemplate,
                apiKey: this.getElementValue('provider-api-key'),
                baseUrl: this.getElementValue('provider-base-url'),
                model: this.getElementValue('provider-model'),
                maxTokens: parseInt(this.getElementValue('provider-max-tokens')),
                enabled: true,
                isDefault: this.llmProviders.length === 0
            };

            this.llmProviders.push(newProvider);
            await this.saveLLMProviders();
            this.renderLLMProviders();
            this.hideAddProviderModal();

            this.showNotification(`${newProvider.name} provider added successfully!`, 'success');
        } catch (error) {
            console.error('Error adding provider:', error);
            this.showNotification('Error adding provider', 'error');
        }
    }

    async deleteProvider(providerId) {
        if (confirm('Are you sure you want to delete this provider?')) {
            try {
                this.llmProviders = this.llmProviders.filter(p => p.id !== providerId);
                await this.saveLLMProviders();
                this.renderLLMProviders();

                this.showNotification('Provider deleted successfully!', 'success');
            } catch (error) {
                console.error('Error deleting provider:', error);
                this.showNotification('Error deleting provider', 'error');
            }
        }
    }

    async updateProviderField(providerId, field, value) {
        try {
            const provider = this.llmProviders.find(p => p.id === providerId);
            if (provider) {
                provider[field] = value;
                await this.saveLLMProviders();

                // Update UI to show connection status
                setTimeout(() => {
                    this.renderLLMProviders();
                }, 100);
            }
        } catch (error) {
            console.error('Error updating provider:', error);
        }
    }

    editProvider(providerId) {
        // For now, just focus on the API key field
        const providerElement = document.querySelector(`[data-provider-id="${providerId}"]`);
        if (providerElement) {
            const apiKeyInput = providerElement.closest('.llm-provider-item').querySelector('input[data-field="apiKey"]');
            if (apiKeyInput) {
                apiKeyInput.focus();
            }
        }
    }

    resetAddProviderForm() {
        document.getElementById('provider-name').value = '';
        document.getElementById('provider-api-key').value = '';
        document.getElementById('provider-base-url').value = '';
        document.getElementById('provider-model').value = '';
        document.getElementById('provider-max-tokens').value = '4000';

        document.querySelectorAll('.provider-template').forEach(template => {
            template.classList.remove('selected');
        });

        const form = document.getElementById('provider-form');
        if (form) {
            form.classList.add('hidden');
        }
    }

    // Utility methods
    getElementValue(id) {
        const element = document.getElementById(id);
        return element ? element.value : '';
    }

    setElementText(id, text) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = text;
        }
    }

    setElementHTML(id, html) {
        const element = document.getElementById(id);
        if (element) {
            element.innerHTML = html;
        }
    }

    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            const text = overlay.querySelector('.loading-text');
            if (text) {
                text.textContent = message;
            }
            overlay.classList.remove('hidden');
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    showNotification(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        alert(message); // Simple notification for now
    }

    updateUI() {
        // Update UI based on current state
        console.log('UI updated');
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing popup...');
    new SimplePopup();
});

console.log('Popup script loaded');
