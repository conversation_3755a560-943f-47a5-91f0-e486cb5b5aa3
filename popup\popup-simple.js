// AI SEO Optimizer - Simple Popup Script

console.log('AI SEO Optimizer popup loading...');

class SimplePopup {
    constructor() {
        this.currentTab = 'dashboard';
        this.settings = {};
        this.analysisData = null;
        
        this.init();
    }

    async init() {
        try {
            await this.loadSettings();
            this.setupEventListeners();
            this.setupTabNavigation();
            this.updateUI();
            console.log('Popup initialized successfully');
        } catch (error) {
            console.error('Error initializing popup:', error);
        }
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Dashboard actions
        const analyzeBtn = document.getElementById('analyze-page');
        if (analyzeBtn) {
            analyzeBtn.addEventListener('click', () => {
                this.analyzePage();
            });
        }

        const generateBtn = document.getElementById('generate-keywords');
        if (generateBtn) {
            generateBtn.addEventListener('click', () => {
                this.generateKeywords();
            });
        }

        // Settings
        const saveBtn = document.getElementById('save-settings');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }

        const resetBtn = document.getElementById('reset-settings');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetSettings();
            });
        }

        // Keyword density slider
        const densitySlider = document.getElementById('keyword-density');
        const densityValue = document.getElementById('density-value');
        if (densitySlider && densityValue) {
            densitySlider.addEventListener('input', (e) => {
                densityValue.textContent = e.target.value + '%';
            });
        }
    }

    setupTabNavigation() {
        this.switchTab('dashboard');
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const activeBtn = document.querySelector(`[data-tab="${tabName}"]`);
        if (activeBtn) {
            activeBtn.classList.add('active');
        }

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        const activeContent = document.getElementById(tabName);
        if (activeContent) {
            activeContent.classList.add('active');
        }

        this.currentTab = tabName;
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get([
                'openaiKey',
                'openrouterKey',
                'targetKeywords',
                'keywordDensity'
            ]);

            this.settings = {
                openaiKey: result.openaiKey || '',
                openrouterKey: result.openrouterKey || '',
                targetKeywords: result.targetKeywords || '',
                keywordDensity: result.keywordDensity || 2
            };

            this.updateSettingsUI();
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    updateSettingsUI() {
        const elements = {
            'openai-key': this.settings.openaiKey,
            'openrouter-key': this.settings.openrouterKey,
            'target-keywords': this.settings.targetKeywords,
            'keyword-density': this.settings.keywordDensity
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.value = value;
            }
        });

        const densityValue = document.getElementById('density-value');
        if (densityValue) {
            densityValue.textContent = this.settings.keywordDensity + '%';
        }
    }

    async saveSettings() {
        try {
            const settings = {
                openaiKey: this.getElementValue('openai-key'),
                openrouterKey: this.getElementValue('openrouter-key'),
                targetKeywords: this.getElementValue('target-keywords'),
                keywordDensity: parseFloat(this.getElementValue('keyword-density'))
            };

            await chrome.storage.sync.set(settings);
            this.settings = settings;

            this.showNotification('Settings saved successfully!', 'success');
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showNotification('Error saving settings', 'error');
        }
    }

    async resetSettings() {
        try {
            await chrome.storage.sync.clear();
            
            this.settings = {
                openaiKey: '',
                openrouterKey: '',
                targetKeywords: '',
                keywordDensity: 2
            };

            this.updateSettingsUI();
            this.showNotification('Settings reset to default', 'success');
        } catch (error) {
            console.error('Error resetting settings:', error);
            this.showNotification('Error resetting settings', 'error');
        }
    }

    async analyzePage() {
        this.showLoading('Analyzing page...');

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const response = await chrome.runtime.sendMessage({
                action: 'analyzePage',
                data: {
                    pageData: await this.getPageData(tab.id),
                    targetKeywords: this.settings.targetKeywords.split(',').map(k => k.trim()).filter(k => k)
                }
            });

            if (response.success) {
                this.analysisData = response.data;
                this.updateDashboard(response.data);
                this.updateAnalysisTab(response.data);
            } else {
                throw new Error(response.error || 'Analysis failed');
            }

        } catch (error) {
            console.error('Analysis error:', error);
            this.showNotification('Error analyzing page: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async generateKeywords() {
        this.showLoading('Generating keywords...');

        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            const response = await chrome.runtime.sendMessage({
                action: 'generateKeywords',
                data: { pageData: await this.getPageData(tab.id) }
            });

            if (response.success) {
                const keywords = [
                    ...response.data.primaryKeywords,
                    ...response.data.semanticKeywords
                ];
                
                const keywordsElement = document.getElementById('target-keywords');
                if (keywordsElement) {
                    keywordsElement.value = keywords.join(', ');
                }
                
                this.showNotification('Keywords generated successfully!', 'success');
            } else {
                throw new Error(response.error || 'Keyword generation failed');
            }

        } catch (error) {
            console.error('Keyword generation error:', error);
            this.showNotification('Error generating keywords: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    }

    async getPageData(tabId) {
        const response = await chrome.runtime.sendMessage({
            action: 'getPageData'
        });
        
        if (response.success) {
            return response.data;
        } else {
            throw new Error(response.error || 'Failed to get page data');
        }
    }

    updateDashboard(analysis) {
        this.setElementText('seo-score', analysis.seoScore || '--');
        this.setElementText('keyword-score', analysis.keywordScore || '--');
        this.setElementText('content-score', analysis.contentScore || '--');
        this.setElementText('technical-score', analysis.technicalScore || '--');

        // Update recommendations
        const recommendationsList = document.getElementById('recommendations-list');
        if (recommendationsList && analysis.recommendations) {
            recommendationsList.innerHTML = '';

            analysis.recommendations.forEach(rec => {
                const recElement = document.createElement('div');
                recElement.className = 'recommendation-item';
                recElement.innerHTML = `
                    <div class="rec-icon">${this.getRecommendationIcon(rec.type)}</div>
                    <div class="rec-content">
                        <div class="rec-title">${rec.title}</div>
                        <div class="rec-description">${rec.description}</div>
                    </div>
                `;
                recommendationsList.appendChild(recElement);
            });
        }
    }

    updateAnalysisTab(analysis) {
        // Simple analysis display
        this.setElementHTML('meta-analysis', '<div class="analysis-result">Analysis complete - check dashboard for results</div>');
        this.setElementHTML('headings-analysis', '<div class="analysis-result">Analysis complete - check dashboard for results</div>');
        this.setElementHTML('keyword-analysis', '<div class="analysis-result">Analysis complete - check dashboard for results</div>');
        this.setElementHTML('content-analysis', '<div class="analysis-result">Analysis complete - check dashboard for results</div>');
    }

    getRecommendationIcon(type) {
        const icons = {
            title: '📝',
            meta: '🏷️',
            heading: '📋',
            content: '📄',
            image: '🖼️',
            link: '🔗',
            keyword: '🎯',
            technical: '⚙️'
        };
        return icons[type] || '💡';
    }

    // Utility methods
    getElementValue(id) {
        const element = document.getElementById(id);
        return element ? element.value : '';
    }

    setElementText(id, text) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = text;
        }
    }

    setElementHTML(id, html) {
        const element = document.getElementById(id);
        if (element) {
            element.innerHTML = html;
        }
    }

    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            const text = overlay.querySelector('.loading-text');
            if (text) {
                text.textContent = message;
            }
            overlay.classList.remove('hidden');
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loading-overlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    showNotification(message, type = 'info') {
        console.log(`${type.toUpperCase()}: ${message}`);
        alert(message); // Simple notification for now
    }

    updateUI() {
        // Update UI based on current state
        console.log('UI updated');
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing popup...');
    new SimplePopup();
});

console.log('Popup script loaded');
