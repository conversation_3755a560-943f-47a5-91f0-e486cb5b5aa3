{"name": "ai-seo-optimizer", "version": "1.0.0", "description": "An AI-powered Chrome extension for SEO optimization", "main": "background/background.js", "scripts": {"build": "echo 'No build process needed for basic extension'", "test": "echo 'Tests not implemented yet'", "lint": "eslint .", "format": "prettier --write .", "dev": "echo 'Load extension in Chrome Developer Mode'", "package": "zip -r ai-seo-optimizer.zip . -x node_modules/\\* .git/\\* *.zip"}, "keywords": ["seo", "chrome-extension", "ai", "optimization", "content-analysis", "keyword-research"], "author": "Your Name", "license": "MIT", "devDependencies": {"eslint": "^8.0.0", "prettier": "^2.8.0", "@types/chrome": "^0.0.246"}, "repository": {"type": "git", "url": "https://github.com/yourusername/ai-seo-optimizer"}, "bugs": {"url": "https://github.com/yourusername/ai-seo-optimizer/issues"}, "homepage": "https://github.com/yourusername/ai-seo-optimizer#readme", "engines": {"node": ">=14.0.0"}}