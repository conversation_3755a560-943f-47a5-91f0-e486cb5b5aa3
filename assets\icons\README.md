# Icons Directory

This directory should contain the extension icons in the following sizes:

- `icon16.png` - 16x16 pixels (toolbar icon)
- `icon32.png` - 32x32 pixels (popup and settings)
- `icon48.png` - 48x48 pixels (extension management page)
- `icon128.png` - 128x128 pixels (Chrome Web Store)

## Icon Requirements

- **Format**: PNG with transparency
- **Style**: Modern, clean design representing SEO/optimization
- **Colors**: Should match the extension's color scheme (blues/purples)
- **Content**: Could include elements like:
  - Magnifying glass (analysis)
  - Graph/chart (optimization)
  - Gear/cog (technical SEO)
  - Target/bullseye (keyword targeting)

## Temporary Solution

For development purposes, you can use any placeholder icons or create simple colored squares with the appropriate dimensions until proper icons are designed.

## Creating Icons

You can create icons using:
- Adobe Illustrator/Photoshop
- Figma
- Canva
- Free online icon generators
- AI image generators

Make sure all icons are consistent in style and clearly represent the extension's purpose.
