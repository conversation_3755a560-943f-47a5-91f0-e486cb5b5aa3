/* AI SEO Optimizer - Professional Design */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --warning-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
}

body {
    width: 380px;
    height: 600px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.5;
    overflow: hidden;
}

.container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    overflow: hidden;
}

/* Header Styles */
.header {
    background: var(--primary-gradient);
    color: white;
    padding: 16px 20px;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 14px;
    position: relative;
    z-index: 1;
}

.logo-icon {
    width: 24px;
    height: 24px;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.logo h1 {
    font-size: 18px;
    font-weight: 700;
    letter-spacing: -0.025em;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.nav-tabs {
    display: flex;
    gap: 6px;
    position: relative;
    z-index: 1;
}

.tab-btn {
    flex: 1;
    padding: 10px 14px;
    border: none;
    background: rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.9);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 13px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.tab-btn.active {
    background: var(--bg-primary);
    color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: var(--bg-secondary);
}

.main-content::-webkit-scrollbar {
    width: 6px;
}

.main-content::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

.main-content::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
    background: var(--text-light);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Dashboard Styles */
.seo-score-card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: 24px;
    margin-bottom: 24px;
    display: flex;
    align-items: center;
    gap: 20px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.seo-score-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--secondary-gradient);
}

.score-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: 800;
    color: white;
    position: relative;
    box-shadow: var(--shadow-md);
}

.score-circle::after {
    content: '';
    position: absolute;
    inset: 4px;
    border-radius: 50%;
    border: 2px solid rgba(255,255,255,0.3);
}

.score-details {
    flex: 1;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 13px;
}

.score-item:last-child {
    margin-bottom: 0;
}

.score-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.score-value {
    font-weight: 700;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 12px;
}

.score-value.good {
    background: #dcfce7;
    color: #166534;
}

.score-value.medium {
    background: #fef3c7;
    color: #92400e;
}

.score-value.poor {
    background: #fee2e2;
    color: #991b1b;
}

/* Action Buttons */
.quick-actions {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
}

.action-btn {
    flex: 1;
    padding: 14px 18px;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-size: 13px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* Recommendations */
.recommendations-section h3 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 16px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.recommendations-section h3::before {
    content: '💡';
    font-size: 18px;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    margin-bottom: 12px;
    border-left: 4px solid var(--primary-color);
    box-shadow: var(--shadow-sm);
    transition: all 0.2s ease;
}

.recommendation-item:hover {
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.rec-icon {
    font-size: 20px;
    margin-top: 2px;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
}

.rec-content {
    flex: 1;
}

.rec-title {
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.rec-description {
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
}

/* Analysis Styles */
.analysis-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 20px;
}

.analysis-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 18px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
    transition: all 0.2s ease;
}

.analysis-section:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.analysis-section h5 {
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 12px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.score-badge {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    border-radius: var(--radius-md);
    font-size: 11px;
    font-weight: 700;
    margin-bottom: 12px;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.score-badge.score-good {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
    border: 1px solid #86efac;
}

.score-badge.score-medium {
    background: linear-gradient(135deg, #fef3c7, #fde68a);
    color: #92400e;
    border: 1px solid #fbbf24;
}

.score-badge.score-poor {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
    border: 1px solid #f87171;
}

.detail-item {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 6px;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--primary-color);
}

.suggestions {
    font-size: 11px;
    color: #166534;
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    padding: 8px 12px;
    border-radius: var(--radius-sm);
    margin-top: 8px;
    border: 1px solid #86efac;
    font-weight: 500;
}

/* Settings Styles */
.settings-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-light);
}

.settings-section h3 {
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 16px;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.section-header h3 {
    margin: 0;
}

.setting-group {
    margin-bottom: 16px;
}

.setting-group label {
    display: block;
    font-size: 13px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 6px;
}

.setting-group input,
.setting-group select,
.setting-group textarea {
    width: 100%;
    padding: 12px 14px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 13px;
    transition: all 0.2s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.setting-group input:focus,
.setting-group select:focus,
.setting-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

.setting-group small {
    display: block;
    font-size: 11px;
    color: var(--text-light);
    margin-top: 6px;
    font-style: italic;
}

/* LLM Provider Management */
.llm-provider-item {
    background: var(--bg-primary);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.llm-provider-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.llm-provider-item:hover::before,
.llm-provider-item.active::before {
    transform: scaleX(1);
}

.llm-provider-item:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.llm-provider-item.active {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #f0f4ff, #e0e7ff);
}

.provider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.provider-name {
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.provider-status {
    padding: 4px 10px;
    border-radius: var(--radius-md);
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.provider-status.connected {
    background: linear-gradient(135deg, #dcfce7, #bbf7d0);
    color: #166534;
    border: 1px solid #86efac;
}

.provider-status.disconnected {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    color: #991b1b;
    border: 1px solid #f87171;
}

.provider-actions {
    display: flex;
    gap: 6px;
}

.btn-icon {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    padding: 8px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    color: var(--text-secondary);
    font-size: 14px;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    transform: scale(1.1);
}

.btn-icon.delete:hover {
    background: #fee2e2;
    color: #991b1b;
    border-color: #f87171;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
    backdrop-filter: blur(4px);
}

.modal-overlay.hidden {
    display: none;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: 24px;
    width: 90%;
    max-width: 340px;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-light);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 2px solid var(--border-light);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 700;
}

.modal-close {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    font-size: 20px;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 8px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-sm);
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.provider-templates {
    display: grid;
    gap: 12px;
    margin-bottom: 20px;
}

.provider-template {
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--bg-secondary);
}

.provider-template:hover,
.provider-template.selected {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #f0f4ff, #e0e7ff);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.template-name {
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
    font-size: 14px;
}

.template-description {
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 8px;
    line-height: 1.4;
}

.template-models {
    font-size: 10px;
    color: var(--text-light);
    font-style: italic;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
}

.loading-overlay.hidden {
    display: none;
}

.loading-content {
    text-align: center;
    color: white;
    background: rgba(0, 0, 0, 0.5);
    padding: 24px;
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
    font-weight: 600;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-0 {
    margin-bottom: 0 !important;
}

/* Responsive Design */
@media (max-height: 650px) {
    .main-content {
        padding: 16px;
    }

    .seo-score-card {
        padding: 18px;
        margin-bottom: 18px;
    }

    .score-circle {
        width: 60px;
        height: 60px;
        font-size: 18px;
    }
}
