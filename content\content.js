// AI SEO Optimizer - Content Script

class SEOOptimizerContent {
    constructor() {
        this.isAnalyzing = false;
        this.highlightedElements = [];
        this.seoOverlay = null;
        
        this.init();
    }

    init() {
        this.setupMessageListener();
        this.createSEOOverlay();
        this.observePageChanges();
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // Keep message channel open
        });
    }

    async handleMessage(request, sender, sendResponse) {
        try {
            switch (request.action) {
                case 'extractPageData':
                    const pageData = this.extractPageData();
                    sendResponse({ success: true, data: pageData });
                    break;

                case 'highlightSEOIssues':
                    this.highlightSEOIssues(request.data);
                    sendResponse({ success: true });
                    break;

                case 'showSEOSuggestions':
                    this.showSEOSuggestions(request.data);
                    sendResponse({ success: true });
                    break;

                case 'clearHighlights':
                    this.clearHighlights();
                    sendResponse({ success: true });
                    break;

                case 'selectionAnalysis':
                    this.showSelectionAnalysis(request.data);
                    sendResponse({ success: true });
                    break;

                case 'toggleSEOMode':
                    this.toggleSEOMode(request.enabled);
                    sendResponse({ success: true });
                    break;

                default:
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message in content script:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    extractPageData() {
        const data = {
            url: window.location.href,
            title: document.title,
            metaDescription: this.getMetaContent('description'),
            metaKeywords: this.getMetaContent('keywords'),
            metaRobots: this.getMetaContent('robots'),
            canonicalUrl: this.getCanonicalUrl(),
            
            headings: this.extractHeadings(),
            content: this.extractContent(),
            wordCount: this.getWordCount(),
            
            images: this.extractImages(),
            links: this.extractLinks(),
            
            structuredData: this.extractStructuredData(),
            openGraph: this.extractOpenGraph(),
            twitterCard: this.extractTwitterCard(),
            
            pageSpeed: this.getPageSpeedMetrics(),
            accessibility: this.checkAccessibility()
        };

        return data;
    }

    getMetaContent(name) {
        const meta = document.querySelector(`meta[name="${name}"], meta[property="${name}"]`);
        return meta ? meta.content : '';
    }

    getCanonicalUrl() {
        const canonical = document.querySelector('link[rel="canonical"]');
        return canonical ? canonical.href : '';
    }

    extractHeadings() {
        const headings = {};
        ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].forEach(tag => {
            headings[tag] = Array.from(document.querySelectorAll(tag)).map(el => ({
                text: el.textContent.trim(),
                element: el,
                position: this.getElementPosition(el)
            }));
        });
        return headings;
    }

    extractContent() {
        // Get main content, excluding navigation, footer, sidebar
        const contentSelectors = [
            'main',
            'article',
            '[role="main"]',
            '.content',
            '.main-content',
            '#content',
            '#main'
        ];

        let mainContent = null;
        for (const selector of contentSelectors) {
            mainContent = document.querySelector(selector);
            if (mainContent) break;
        }

        const content = mainContent || document.body;
        
        return {
            text: content.innerText,
            html: content.innerHTML,
            paragraphs: Array.from(content.querySelectorAll('p')).map(p => p.textContent.trim()),
            lists: Array.from(content.querySelectorAll('ul, ol')).length
        };
    }

    getWordCount() {
        const text = document.body.innerText;
        return text.split(/\s+/).filter(word => word.length > 0).length;
    }

    extractImages() {
        return Array.from(document.querySelectorAll('img')).map(img => ({
            src: img.src,
            alt: img.alt || '',
            title: img.title || '',
            width: img.naturalWidth || img.width,
            height: img.naturalHeight || img.height,
            loading: img.loading || '',
            element: img,
            hasAltText: !!img.alt,
            isDecorative: img.alt === '' && img.role === 'presentation'
        }));
    }

    extractLinks() {
        return Array.from(document.querySelectorAll('a[href]')).map(link => ({
            href: link.href,
            text: link.textContent.trim(),
            title: link.title || '',
            target: link.target || '',
            rel: link.rel || '',
            isInternal: link.hostname === window.location.hostname,
            isExternal: link.hostname !== window.location.hostname,
            hasNoFollow: link.rel.includes('nofollow'),
            element: link
        }));
    }

    extractStructuredData() {
        const scripts = document.querySelectorAll('script[type="application/ld+json"]');
        const structuredData = [];
        
        scripts.forEach(script => {
            try {
                const data = JSON.parse(script.textContent);
                structuredData.push(data);
            } catch (error) {
                console.warn('Invalid structured data:', error);
            }
        });
        
        return structuredData;
    }

    extractOpenGraph() {
        const ogTags = {};
        document.querySelectorAll('meta[property^="og:"]').forEach(meta => {
            const property = meta.getAttribute('property').replace('og:', '');
            ogTags[property] = meta.content;
        });
        return ogTags;
    }

    extractTwitterCard() {
        const twitterTags = {};
        document.querySelectorAll('meta[name^="twitter:"]').forEach(meta => {
            const name = meta.getAttribute('name').replace('twitter:', '');
            twitterTags[name] = meta.content;
        });
        return twitterTags;
    }

    getPageSpeedMetrics() {
        // Basic performance metrics
        const navigation = performance.getEntriesByType('navigation')[0];
        return {
            loadTime: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
            domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
            firstPaint: this.getFirstPaint(),
            resourceCount: performance.getEntriesByType('resource').length
        };
    }

    getFirstPaint() {
        const paintEntries = performance.getEntriesByType('paint');
        const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
        return firstPaint ? firstPaint.startTime : 0;
    }

    checkAccessibility() {
        return {
            missingAltImages: document.querySelectorAll('img:not([alt])').length,
            emptyLinks: document.querySelectorAll('a:empty, a[href="#"]').length,
            missingHeadings: document.querySelectorAll('h1').length === 0,
            lowContrastElements: this.findLowContrastElements().length
        };
    }

    findLowContrastElements() {
        // Basic contrast checking - would need more sophisticated implementation
        const elements = [];
        document.querySelectorAll('p, span, div, a').forEach(el => {
            const style = window.getComputedStyle(el);
            const color = style.color;
            const backgroundColor = style.backgroundColor;
            
            // Simple check - would need proper contrast ratio calculation
            if (color && backgroundColor && color !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
                // Add to elements if contrast is potentially low
                elements.push(el);
            }
        });
        return elements;
    }

    highlightSEOIssues(issues) {
        this.clearHighlights();
        
        issues.forEach(issue => {
            switch (issue.type) {
                case 'missing-alt':
                    this.highlightMissingAltImages();
                    break;
                case 'empty-links':
                    this.highlightEmptyLinks();
                    break;
                case 'heading-structure':
                    this.highlightHeadingIssues();
                    break;
                case 'keyword-density':
                    this.highlightKeywordDensity(issue.keywords);
                    break;
            }
        });
    }

    highlightMissingAltImages() {
        document.querySelectorAll('img:not([alt])').forEach(img => {
            this.addHighlight(img, 'missing-alt', 'Missing alt text');
        });
    }

    highlightEmptyLinks() {
        document.querySelectorAll('a:empty, a[href="#"]').forEach(link => {
            this.addHighlight(link, 'empty-link', 'Empty or invalid link');
        });
    }

    highlightHeadingIssues() {
        const h1s = document.querySelectorAll('h1');
        if (h1s.length === 0) {
            // No H1 found
            const firstHeading = document.querySelector('h2, h3, h4, h5, h6');
            if (firstHeading) {
                this.addHighlight(firstHeading, 'missing-h1', 'Consider using H1 for main heading');
            }
        } else if (h1s.length > 1) {
            // Multiple H1s
            h1s.forEach((h1, index) => {
                if (index > 0) {
                    this.addHighlight(h1, 'multiple-h1', 'Multiple H1 tags found');
                }
            });
        }
    }

    highlightKeywordDensity(keywords) {
        if (!keywords || keywords.length === 0) return;
        
        const textNodes = this.getTextNodes(document.body);
        keywords.forEach(keyword => {
            textNodes.forEach(node => {
                if (node.textContent.toLowerCase().includes(keyword.toLowerCase())) {
                    this.highlightKeywordInText(node, keyword);
                }
            });
        });
    }

    addHighlight(element, type, message) {
        element.classList.add('seo-highlight', `seo-${type}`);
        element.setAttribute('data-seo-message', message);
        this.highlightedElements.push(element);
        
        // Add tooltip
        this.addTooltip(element, message);
    }

    addTooltip(element, message) {
        const tooltip = document.createElement('div');
        tooltip.className = 'seo-tooltip';
        tooltip.textContent = message;
        
        element.addEventListener('mouseenter', () => {
            document.body.appendChild(tooltip);
            const rect = element.getBoundingClientRect();
            tooltip.style.left = rect.left + 'px';
            tooltip.style.top = (rect.top - tooltip.offsetHeight - 5) + 'px';
        });
        
        element.addEventListener('mouseleave', () => {
            if (tooltip.parentNode) {
                tooltip.parentNode.removeChild(tooltip);
            }
        });
    }

    clearHighlights() {
        this.highlightedElements.forEach(el => {
            el.classList.remove('seo-highlight', 'seo-missing-alt', 'seo-empty-link', 'seo-missing-h1', 'seo-multiple-h1');
            el.removeAttribute('data-seo-message');
        });
        this.highlightedElements = [];
        
        // Remove tooltips
        document.querySelectorAll('.seo-tooltip').forEach(tooltip => {
            tooltip.remove();
        });
    }

    createSEOOverlay() {
        this.seoOverlay = document.createElement('div');
        this.seoOverlay.id = 'seo-optimizer-overlay';
        this.seoOverlay.className = 'seo-overlay hidden';
        
        this.seoOverlay.innerHTML = `
            <div class="seo-overlay-content">
                <div class="seo-overlay-header">
                    <h3>SEO Analysis</h3>
                    <button class="seo-close-btn">&times;</button>
                </div>
                <div class="seo-overlay-body">
                    <div id="seo-suggestions"></div>
                </div>
            </div>
        `;
        
        document.body.appendChild(this.seoOverlay);
        
        // Add close functionality
        this.seoOverlay.querySelector('.seo-close-btn').addEventListener('click', () => {
            this.hideSEOOverlay();
        });
    }

    showSEOSuggestions(suggestions) {
        const suggestionsContainer = this.seoOverlay.querySelector('#seo-suggestions');
        suggestionsContainer.innerHTML = '';
        
        suggestions.forEach(suggestion => {
            const suggestionEl = document.createElement('div');
            suggestionEl.className = 'seo-suggestion';
            suggestionEl.innerHTML = `
                <div class="suggestion-icon">${this.getSuggestionIcon(suggestion.type)}</div>
                <div class="suggestion-content">
                    <h4>${suggestion.title}</h4>
                    <p>${suggestion.description}</p>
                    <span class="suggestion-priority ${suggestion.priority}">${suggestion.priority}</span>
                </div>
            `;
            suggestionsContainer.appendChild(suggestionEl);
        });
        
        this.showSEOOverlay();
    }

    showSEOOverlay() {
        this.seoOverlay.classList.remove('hidden');
    }

    hideSEOOverlay() {
        this.seoOverlay.classList.add('hidden');
    }

    showSelectionAnalysis(analysis) {
        const notification = document.createElement('div');
        notification.className = 'seo-selection-analysis';
        notification.innerHTML = `
            <div class="analysis-content">
                <h4>Selection Analysis</h4>
                <p>Words: ${analysis.wordCount}</p>
                <p>Characters: ${analysis.characterCount}</p>
                <p>Readability Score: ${analysis.readabilityScore}/100</p>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    toggleSEOMode(enabled) {
        if (enabled) {
            document.body.classList.add('seo-mode-active');
        } else {
            document.body.classList.remove('seo-mode-active');
            this.clearHighlights();
            this.hideSEOOverlay();
        }
    }

    observePageChanges() {
        // Watch for dynamic content changes
        const observer = new MutationObserver((mutations) => {
            let shouldReanalyze = false;
            
            mutations.forEach(mutation => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Check if significant content was added
                    mutation.addedNodes.forEach(node => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            const hasContent = node.textContent && node.textContent.trim().length > 50;
                            if (hasContent) {
                                shouldReanalyze = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldReanalyze) {
                // Debounce reanalysis
                clearTimeout(this.reanalysisTimeout);
                this.reanalysisTimeout = setTimeout(() => {
                    this.notifyContentChange();
                }, 2000);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    notifyContentChange() {
        chrome.runtime.sendMessage({
            action: 'contentChanged',
            url: window.location.href
        });
    }

    getElementPosition(element) {
        const rect = element.getBoundingClientRect();
        return {
            top: rect.top + window.scrollY,
            left: rect.left + window.scrollX
        };
    }

    getTextNodes(element) {
        const textNodes = [];
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );
        
        let node;
        while (node = walker.nextNode()) {
            if (node.textContent.trim()) {
                textNodes.push(node);
            }
        }
        
        return textNodes;
    }

    highlightKeywordInText(textNode, keyword) {
        const parent = textNode.parentNode;
        const text = textNode.textContent;
        const regex = new RegExp(`(${keyword})`, 'gi');
        
        if (regex.test(text)) {
            const highlightedHTML = text.replace(regex, '<mark class="seo-keyword-highlight">$1</mark>');
            const wrapper = document.createElement('span');
            wrapper.innerHTML = highlightedHTML;
            parent.replaceChild(wrapper, textNode);
        }
    }

    getSuggestionIcon(type) {
        const icons = {
            title: '📝',
            meta: '🏷️',
            heading: '📋',
            content: '📄',
            image: '🖼️',
            link: '🔗',
            keyword: '🎯',
            technical: '⚙️'
        };
        return icons[type] || '💡';
    }
}

// Initialize content script
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new SEOOptimizerContent();
    });
} else {
    new SEOOptimizerContent();
}
