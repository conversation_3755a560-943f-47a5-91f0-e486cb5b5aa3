<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI SEO Optimizer - Size Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }
        .popup-frame {
            border: 2px solid #667eea;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            max-width: 300px;
        }
        .size-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
            border-left: 4px solid #2196f3;
        }
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        h3 {
            color: #667eea;
            margin-bottom: 10px;
        }
        ul {
            margin: 10px 0;
            padding-left: 20px;
        }
        li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>🔧 AI SEO Optimizer - Popup Size Test</h1>
    
    <div class="test-container">
        <div class="popup-frame">
            <iframe src="popup/popup.html" width="380" height="600" frameborder="0"></iframe>
        </div>
        
        <div class="info">
            <h3>📏 Size Specifications</h3>
            <div class="size-info">
                <strong>Fixed Dimensions:</strong><br>
                Width: <span class="code">380px</span><br>
                Height: <span class="code">600px</span>
            </div>
            
            <div class="status success">
                ✅ <strong>Fixed Issues:</strong><br>
                • Removed viewport height (100vh)<br>
                • Set fixed dimensions<br>
                • Optimized padding and margins<br>
                • Improved scrolling behavior
            </div>
            
            <h3>🎯 What Should Work Now</h3>
            <ul>
                <li>✅ Full popup visible in Chrome extension</li>
                <li>✅ All tabs accessible</li>
                <li>✅ Proper scrolling in content areas</li>
                <li>✅ LLM provider management</li>
                <li>✅ Analysis and dashboard features</li>
            </ul>
            
            <h3>🧪 Test Steps</h3>
            <ol>
                <li>Reload extension in Chrome</li>
                <li>Click extension icon</li>
                <li>Verify full popup is visible</li>
                <li>Test all three tabs</li>
                <li>Try adding LLM providers</li>
                <li>Test analysis features</li>
            </ol>
            
            <div class="size-info">
                <strong>Chrome Extension Limits:</strong><br>
                Max Width: <span class="code">800px</span><br>
                Max Height: <span class="code">600px</span><br>
                Our Size: <span class="code">380x600px</span> ✅
            </div>
            
            <h3>📱 Responsive Design</h3>
            <p>The popup is optimized for:</p>
            <ul>
                <li>Chrome extension popup window</li>
                <li>Compact interface design</li>
                <li>Touch-friendly buttons</li>
                <li>Readable text at small sizes</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('Popup size test loaded');
        console.log('Expected dimensions: 380x600px');
        
        // Check if popup loads correctly
        const iframe = document.querySelector('iframe');
        iframe.onload = function() {
            console.log('✅ Popup loaded successfully');
            console.log('Frame dimensions:', iframe.width + 'x' + iframe.height);
        };
        
        iframe.onerror = function() {
            console.error('❌ Failed to load popup');
        };
    </script>
</body>
</html>
