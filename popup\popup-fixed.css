/* AI SEO Optimizer - Fixed Popup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 380px;
    height: 600px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    margin: 0;
    padding: 0;
    overflow: hidden;
}

.container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    background: white;
    margin: 0;
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 12px 16px;
    flex-shrink: 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.logo-icon {
    width: 20px;
    height: 20px;
}

.logo h1 {
    font-size: 16px;
    font-weight: 600;
}

.nav-tabs {
    display: flex;
    gap: 4px;
}

.tab-btn {
    flex: 1;
    padding: 6px 10px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.tab-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.tab-btn.active {
    background: white;
    color: #667eea;
}

.main-content {
    flex: 1;
    padding: 14px;
    overflow-y: auto;
    height: calc(100% - 100px);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Dashboard Styles */
.seo-score-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 10px;
    padding: 14px;
    color: white;
    margin-bottom: 14px;
    display: flex;
    align-items: center;
    gap: 14px;
}

.score-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    flex-shrink: 0;
}

.score-details {
    flex: 1;
}

.score-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    font-size: 12px;
}

.score-item:last-child {
    margin-bottom: 0;
}

.quick-actions {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
}

.action-btn {
    flex: 1;
    padding: 10px 12px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    transition: all 0.2s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

.btn-secondary:hover {
    background: #e9ecef;
}

.recommendations-section h3 {
    font-size: 14px;
    margin-bottom: 10px;
    color: #333;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 8px;
    border-left: 3px solid #667eea;
}

.rec-icon {
    font-size: 16px;
    margin-top: 1px;
    flex-shrink: 0;
}

.rec-content {
    flex: 1;
}

.rec-title {
    font-weight: 500;
    font-size: 12px;
    color: #333;
    margin-bottom: 2px;
}

.rec-description {
    font-size: 11px;
    color: #666;
    line-height: 1.3;
}

/* Analysis Styles */
.analysis-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-bottom: 16px;
}

.analysis-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    border-left: 3px solid #667eea;
}

.analysis-section h5 {
    font-size: 12px;
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.score-badge {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
    margin-bottom: 6px;
}

.score-badge.score-good {
    background-color: #d4edda;
    color: #155724;
}

.score-badge.score-medium {
    background-color: #fff3cd;
    color: #856404;
}

.score-badge.score-poor {
    background-color: #f8d7da;
    color: #721c24;
}

.detail-item {
    font-size: 10px;
    color: #666;
    margin-bottom: 4px;
}

.suggestions {
    font-size: 10px;
    color: #155724;
    background-color: #d4edda;
    padding: 4px 6px;
    border-radius: 3px;
    margin-top: 6px;
}

/* Settings Styles */
.settings-section {
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e9ecef;
}

.settings-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.settings-section h3 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

.setting-group {
    margin-bottom: 12px;
}

.setting-group label {
    display: block;
    font-size: 12px;
    font-weight: 500;
    color: #495057;
    margin-bottom: 4px;
}

.setting-group input,
.setting-group select,
.setting-group textarea {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 12px;
    transition: border-color 0.2s ease;
}

.setting-group input:focus,
.setting-group select:focus,
.setting-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.setting-group small {
    display: block;
    font-size: 10px;
    color: #6c757d;
    margin-top: 4px;
}

/* LLM Provider Management */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.section-header h3 {
    margin: 0;
    font-size: 14px;
}

.llm-provider-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 8px;
    position: relative;
}

.llm-provider-item.active {
    border-color: #667eea;
    background: #f0f4ff;
}

.provider-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.provider-name {
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.provider-status {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 9px;
    font-weight: 500;
    text-transform: uppercase;
}

.provider-status.connected {
    background: #d4edda;
    color: #155724;
}

.provider-status.disconnected {
    background: #f8d7da;
    color: #721c24;
}

.provider-actions {
    display: flex;
    gap: 4px;
}

.btn-icon {
    background: none;
    border: none;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    color: #666;
    font-size: 12px;
    transition: all 0.2s;
}

.btn-icon:hover {
    background: #e9ecef;
    color: #333;
}

.btn-icon.delete:hover {
    background: #f8d7da;
    color: #721c24;
}

.provider-config {
    display: grid;
    gap: 8px;
}

.config-row {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 8px;
    align-items: center;
}

.config-row label {
    font-size: 10px;
    color: #666;
    margin: 0;
}

.config-row input,
.config-row select {
    font-size: 10px;
    padding: 4px 6px;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10001;
}

.modal-overlay.hidden {
    display: none;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 16px;
    width: 90%;
    max-width: 320px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 14px;
}

.modal-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.provider-templates {
    display: grid;
    gap: 8px;
    margin-bottom: 16px;
}

.provider-template {
    border: 2px solid #e9ecef;
    border-radius: 6px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.2s;
}

.provider-template:hover,
.provider-template.selected {
    border-color: #667eea;
    background: #f0f4ff;
}

.template-name {
    font-weight: 600;
    color: #333;
    margin-bottom: 3px;
    font-size: 12px;
}

.template-description {
    font-size: 10px;
    color: #666;
    margin-bottom: 6px;
}

.template-models {
    font-size: 9px;
    color: #888;
}

.modal-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #e9ecef;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.loading-overlay.hidden {
    display: none;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 12px;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-0 {
    margin-bottom: 0 !important;
}
