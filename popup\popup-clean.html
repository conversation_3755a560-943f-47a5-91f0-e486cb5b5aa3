<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI SEO Optimizer</title>
    <link rel="stylesheet" href="popup-modern.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <img src="../assets/icons/icon32.png" alt="AI SEO Optimizer" class="logo-icon">
                <h1>AI SEO Optimizer</h1>
            </div>
            <div class="nav-tabs">
                <button class="tab-btn active" data-tab="dashboard">Dashboard</button>
                <button class="tab-btn" data-tab="analysis">Analysis</button>
                <button class="tab-btn" data-tab="settings">Settings</button>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Tab -->
            <div id="dashboard" class="tab-content active">
                <div class="dashboard-grid">
                    <!-- SEO Score Section -->
                    <div class="seo-score-section">
                        <div class="score-header">
                            <div class="main-score" id="seo-score">--</div>
                            <div class="score-label">SEO Score</div>
                        </div>
                        <div class="score-breakdown">
                            <div class="score-item">
                                <div class="score-item-value good" id="keyword-score">--</div>
                                <div class="score-item-label">Keywords</div>
                            </div>
                            <div class="score-item">
                                <div class="score-item-value medium" id="content-score">--</div>
                                <div class="score-item-label">Content</div>
                            </div>
                            <div class="score-item">
                                <div class="score-item-value poor" id="technical-score">--</div>
                                <div class="score-item-label">Technical</div>
                            </div>
                            <div class="score-item">
                                <div class="score-item-value medium" id="overall-health">85</div>
                                <div class="score-item-label">Overall</div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Section -->
                    <div class="action-section">
                        <div class="section-title">
                            🚀 Quick Actions
                        </div>
                        <div class="action-buttons">
                            <button class="action-btn btn-primary" id="analyze-btn">
                                🔍 Analyze Page
                            </button>
                            <button class="action-btn btn-secondary" id="generate-keywords-btn">
                                💡 Keywords
                            </button>
                            <button class="action-btn btn-secondary" id="check-speed-btn">
                                ⚡ Speed Test
                            </button>
                            <button class="action-btn btn-secondary" id="export-report-btn">
                                📊 Export
                            </button>
                        </div>
                    </div>

                    <!-- Recommendations Section -->
                    <div class="recommendations-section">
                        <div class="section-title">
                            💡 Recommendations
                        </div>
                        <div class="recommendations-list" id="recommendations-list">
                            <!-- Recommendations will be populated here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Tab -->
            <div id="analysis" class="tab-content">
                <div class="analysis-container">
                    <!-- Meta Tags Analysis -->
                    <div class="analysis-card">
                        <div class="analysis-header">
                            <div class="analysis-title">📝 Meta Tags</div>
                            <div class="status-badge status-good">Good</div>
                        </div>
                        <div class="analysis-content" id="meta-analysis">
                            <div class="analysis-item">
                                <span class="analysis-label">Title Length</span>
                                <span class="analysis-value">45 chars</span>
                            </div>
                            <div class="analysis-item">
                                <span class="analysis-label">Description</span>
                                <span class="analysis-value">Present</span>
                            </div>
                        </div>
                    </div>

                    <!-- Headings Analysis -->
                    <div class="analysis-card">
                        <div class="analysis-header">
                            <div class="analysis-title">📋 Headings</div>
                            <div class="status-badge status-medium">Medium</div>
                        </div>
                        <div class="analysis-content" id="headings-analysis">
                            <div class="analysis-item">
                                <span class="analysis-label">H1 Tags</span>
                                <span class="analysis-value">1 found</span>
                            </div>
                            <div class="analysis-item">
                                <span class="analysis-label">Structure</span>
                                <span class="analysis-value">Good</span>
                            </div>
                        </div>
                    </div>

                    <!-- Keywords Analysis -->
                    <div class="analysis-card">
                        <div class="analysis-header">
                            <div class="analysis-title">🎯 Keywords</div>
                            <div class="status-badge status-good">Good</div>
                        </div>
                        <div class="analysis-content" id="keyword-analysis">
                            <div class="analysis-item">
                                <span class="analysis-label">Density</span>
                                <span class="analysis-value">2.1%</span>
                            </div>
                            <div class="analysis-item">
                                <span class="analysis-label">Distribution</span>
                                <span class="analysis-value">Even</span>
                            </div>
                        </div>
                    </div>

                    <!-- Content Analysis -->
                    <div class="analysis-card">
                        <div class="analysis-header">
                            <div class="analysis-title">📄 Content</div>
                            <div class="status-badge status-medium">Medium</div>
                        </div>
                        <div class="analysis-content" id="content-analysis">
                            <div class="analysis-item">
                                <span class="analysis-label">Word Count</span>
                                <span class="analysis-value">450 words</span>
                            </div>
                            <div class="analysis-item">
                                <span class="analysis-label">Readability</span>
                                <span class="analysis-value">Good</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings" class="tab-content">
                <div class="settings-container">
                    <!-- API Settings -->
                    <div class="settings-card">
                        <div class="section-title">
                            🔑 API Configuration
                        </div>
                        <div class="form-group">
                            <label class="form-label">Target Keywords</label>
                            <input type="text" class="form-input" id="target-keywords" placeholder="seo, optimization, ai">
                            <div class="form-help">Comma-separated keywords to focus on</div>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Analysis Language</label>
                            <select class="form-input" id="analysis-language">
                                <option value="en">English</option>
                                <option value="ar">Arabic</option>
                                <option value="es">Spanish</option>
                                <option value="fr">French</option>
                            </select>
                        </div>
                    </div>

                    <!-- LLM Providers -->
                    <div class="settings-card">
                        <div class="section-title">
                            🤖 LLM Providers
                            <button class="action-btn btn-primary" id="add-provider-btn" style="margin-left: auto; padding: 6px 12px; font-size: 11px;">
                                + Add Provider
                            </button>
                        </div>
                        <div class="provider-list" id="llm-providers-list">
                            <!-- Providers will be populated here -->
                        </div>
                    </div>

                    <!-- Export Settings -->
                    <div class="settings-card">
                        <div class="section-title">
                            📊 Export Options
                        </div>
                        <div class="action-buttons">
                            <button class="action-btn btn-secondary" id="export-json-btn">
                                📄 Export JSON
                            </button>
                            <button class="action-btn btn-secondary" id="export-pdf-btn">
                                📋 Export PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading hidden">
        <div class="spinner"></div>
        <span>Analyzing page...</span>
    </div>

    <!-- Add Provider Modal -->
    <div id="add-provider-modal" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add LLM Provider</h3>
                <button class="modal-close" id="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="form-label">Provider Name</label>
                    <input type="text" class="form-input" id="provider-name" placeholder="OpenAI">
                </div>
                <div class="form-group">
                    <label class="form-label">API Key</label>
                    <input type="password" class="form-input" id="provider-api-key" placeholder="sk-...">
                </div>
                <div class="form-group">
                    <label class="form-label">Model</label>
                    <select class="form-input" id="provider-model">
                        <option value="gpt-4">GPT-4</option>
                        <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                        <option value="gemini-pro">Gemini Pro</option>
                        <option value="claude-3">Claude 3</option>
                    </select>
                </div>
                <div class="action-buttons" style="margin-top: 20px;">
                    <button class="action-btn btn-secondary" id="cancel-provider">Cancel</button>
                    <button class="action-btn btn-primary" id="save-provider">Save Provider</button>
                </div>
            </div>
        </div>
    </div>

    <script src="popup-simple.js"></script>
</body>
</html>
