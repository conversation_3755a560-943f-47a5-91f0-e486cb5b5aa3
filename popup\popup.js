// AI SEO Optimizer - Popup Script
class SEOOptimizerPopup {
    constructor() {
        this.currentTab = 'dashboard';
        this.settings = {};
        this.analysisData = null;
        
        this.init();
    }

    async init() {
        await this.loadSettings();
        this.setupEventListeners();
        this.setupTabNavigation();
        this.updateUI();
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Dashboard actions
        document.getElementById('analyze-page').addEventListener('click', () => {
            this.analyzePage();
        });

        document.getElementById('generate-keywords').addEventListener('click', () => {
            this.generateKeywords();
        });

        // Settings
        document.getElementById('save-settings').addEventListener('click', () => {
            this.saveSettings();
        });

        document.getElementById('reset-settings').addEventListener('click', () => {
            this.resetSettings();
        });

        document.getElementById('export-settings').addEventListener('click', () => {
            this.exportSettings();
        });

        document.getElementById('import-settings').addEventListener('click', () => {
            document.getElementById('import-file').click();
        });

        document.getElementById('import-file').addEventListener('change', (e) => {
            this.importSettings(e.target.files[0]);
        });

        // Keyword density slider
        const densitySlider = document.getElementById('keyword-density');
        const densityValue = document.getElementById('density-value');
        densitySlider.addEventListener('input', (e) => {
            densityValue.textContent = e.target.value + '%';
        });
    }

    setupTabNavigation() {
        // Set initial active tab
        this.switchTab('dashboard');
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;
    }

    async loadSettings() {
        try {
            const result = await chrome.storage.sync.get([
                'openaiKey',
                'openrouterKey',
                'apiProvider',
                'targetKeywords',
                'keywordDensity',
                'contentGoals',
                'autoAnalysis',
                'highlightIssues',
                'analysisDepth',
                'trackedDomains',
                'saveHistory',
                'showBadge',
                'notifyIssues'
            ]);

            this.settings = {
                openaiKey: result.openaiKey || '',
                openrouterKey: result.openrouterKey || '',
                apiProvider: result.apiProvider || 'auto',
                targetKeywords: result.targetKeywords || '',
                keywordDensity: result.keywordDensity || 2,
                contentGoals: result.contentGoals || 'informational',
                autoAnalysis: result.autoAnalysis || false,
                highlightIssues: result.highlightIssues || true,
                analysisDepth: result.analysisDepth || 'standard',
                trackedDomains: result.trackedDomains || '',
                saveHistory: result.saveHistory || true,
                showBadge: result.showBadge || true,
                notifyIssues: result.notifyIssues || false
            };

            // Update UI with loaded settings
            this.updateSettingsUI();

        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    updateSettingsUI() {
        document.getElementById('openai-key').value = this.settings.openaiKey;
        document.getElementById('openrouter-key').value = this.settings.openrouterKey;
        document.getElementById('api-provider').value = this.settings.apiProvider;
        document.getElementById('target-keywords').value = this.settings.targetKeywords;
        document.getElementById('keyword-density').value = this.settings.keywordDensity;
        document.getElementById('density-value').textContent = this.settings.keywordDensity + '%';
        document.getElementById('content-goals').value = this.settings.contentGoals;
        document.getElementById('auto-analysis').checked = this.settings.autoAnalysis;
        document.getElementById('highlight-issues').checked = this.settings.highlightIssues;
        document.getElementById('analysis-depth').value = this.settings.analysisDepth;
        document.getElementById('tracked-domains').value = this.settings.trackedDomains;
        document.getElementById('save-history').checked = this.settings.saveHistory;
        document.getElementById('show-badge').checked = this.settings.showBadge;
        document.getElementById('notify-issues').checked = this.settings.notifyIssues;
    }

    async saveSettings() {
        try {
            const settings = {
                openaiKey: document.getElementById('openai-key').value,
                openrouterKey: document.getElementById('openrouter-key').value,
                apiProvider: document.getElementById('api-provider').value,
                targetKeywords: document.getElementById('target-keywords').value,
                keywordDensity: parseFloat(document.getElementById('keyword-density').value),
                contentGoals: document.getElementById('content-goals').value,
                autoAnalysis: document.getElementById('auto-analysis').checked,
                highlightIssues: document.getElementById('highlight-issues').checked,
                analysisDepth: document.getElementById('analysis-depth').value,
                trackedDomains: document.getElementById('tracked-domains').value,
                saveHistory: document.getElementById('save-history').checked,
                showBadge: document.getElementById('show-badge').checked,
                notifyIssues: document.getElementById('notify-issues').checked
            };

            await chrome.storage.sync.set(settings);
            this.settings = settings;

            // Notify background script of settings change
            chrome.runtime.sendMessage({
                action: 'settingsUpdated',
                settings: settings
            });

            this.showNotification('Settings saved successfully!', 'success');
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showNotification('Error saving settings', 'error');
        }
    }

    async resetSettings() {
        try {
            await chrome.storage.sync.clear();

            // Reset to default settings
            this.settings = {
                openaiKey: '',
                openrouterKey: '',
                apiProvider: 'auto',
                targetKeywords: '',
                keywordDensity: 2,
                contentGoals: 'informational',
                autoAnalysis: false,
                highlightIssues: true,
                analysisDepth: 'standard',
                trackedDomains: '',
                saveHistory: true,
                showBadge: true,
                notifyIssues: false
            };

            // Update UI
            this.updateSettingsUI();

            this.showNotification('Settings reset to default', 'success');
        } catch (error) {
            console.error('Error resetting settings:', error);
            this.showNotification('Error resetting settings', 'error');
        }
    }

    exportSettings() {
        try {
            const settingsData = {
                version: '1.0',
                timestamp: new Date().toISOString(),
                settings: this.settings
            };

            const dataStr = JSON.stringify(settingsData, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `seo-optimizer-settings-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            this.showNotification('Settings exported successfully', 'success');
        } catch (error) {
            console.error('Error exporting settings:', error);
            this.showNotification('Error exporting settings', 'error');
        }
    }

    async importSettings(file) {
        if (!file) return;

        try {
            const text = await file.text();
            const data = JSON.parse(text);

            if (!data.settings) {
                throw new Error('Invalid settings file format');
            }

            // Validate and merge settings
            const importedSettings = { ...this.settings, ...data.settings };

            // Save imported settings
            await chrome.storage.sync.set(importedSettings);
            this.settings = importedSettings;

            // Update UI
            this.updateSettingsUI();

            this.showNotification('Settings imported successfully', 'success');
        } catch (error) {
            console.error('Error importing settings:', error);
            this.showNotification('Error importing settings: ' + error.message, 'error');
        }
    }

    async analyzePage() {
        if (!this.settings.openaiKey && !this.settings.openrouterKey) {
            this.showNotification('Please configure API keys in Settings first', 'warning');
            this.switchTab('settings');
            return;
        }

        this.showLoading('Analyzing page...');

        try {
            // Get current tab
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            // Inject content script to analyze page
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: this.extractPageData
            });

            const pageData = results[0].result;
            
            // Analyze with AI
            const analysis = await this.performAIAnalysis(pageData);
            
            this.analysisData = analysis;
            this.updateDashboard(analysis);
            this.updateAnalysisTab(analysis);

        } catch (error) {
            console.error('Error analyzing page:', error);
            this.showNotification('Error analyzing page', 'error');
        } finally {
            this.hideLoading();
        }
    }

    extractPageData() {
        // This function runs in the context of the web page
        const data = {
            url: window.location.href,
            title: document.title,
            metaDescription: document.querySelector('meta[name="description"]')?.content || '',
            metaKeywords: document.querySelector('meta[name="keywords"]')?.content || '',
            headings: {
                h1: Array.from(document.querySelectorAll('h1')).map(h => h.textContent.trim()),
                h2: Array.from(document.querySelectorAll('h2')).map(h => h.textContent.trim()),
                h3: Array.from(document.querySelectorAll('h3')).map(h => h.textContent.trim())
            },
            content: document.body.innerText,
            images: Array.from(document.querySelectorAll('img')).map(img => ({
                src: img.src,
                alt: img.alt || '',
                title: img.title || ''
            })),
            links: Array.from(document.querySelectorAll('a')).map(link => ({
                href: link.href,
                text: link.textContent.trim(),
                title: link.title || ''
            }))
        };

        return data;
    }

    async performAIAnalysis(pageData) {
        try {
            // Send analysis request to background script
            const response = await chrome.runtime.sendMessage({
                action: 'analyzePage',
                data: {
                    pageData: pageData,
                    targetKeywords: this.settings.targetKeywords.split(',').map(k => k.trim()).filter(k => k)
                }
            });

            if (response.success) {
                return response.data;
            } else {
                throw new Error(response.error || 'Analysis failed');
            }
        } catch (error) {
            console.error('AI Analysis error:', error);
            // Return fallback analysis
            return {
                seoScore: 50,
                keywordScore: 50,
                contentScore: 50,
                technicalScore: 50,
                recommendations: [
                    {
                        type: 'error',
                        title: 'Analysis Error',
                        description: 'Unable to perform AI analysis. Please check your API configuration in Settings.',
                        priority: 'high'
                    }
                ]
            };
        }
    }

    async generateKeywords() {
        if (!this.settings.openaiKey && !this.settings.openrouterKey) {
            this.showNotification('Please configure API keys in Settings first', 'warning');
            this.switchTab('settings');
            return;
        }

        this.showLoading('Generating keywords...');

        try {
            // Get current page content
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: this.extractPageData
            });

            const pageData = results[0].result;
            
            // Generate keywords with AI (mock implementation)
            const keywords = await this.generateKeywordsWithAI(pageData);
            
            // Update target keywords in settings
            document.getElementById('target-keywords').value = keywords.join(', ');
            
            this.showNotification('Keywords generated successfully!', 'success');

        } catch (error) {
            console.error('Error generating keywords:', error);
            this.showNotification('Error generating keywords', 'error');
        } finally {
            this.hideLoading();
        }
    }

    async generateKeywordsWithAI(pageData) {
        try {
            // Send keyword generation request to background script
            const response = await chrome.runtime.sendMessage({
                action: 'generateKeywords',
                data: { pageData: pageData }
            });

            if (response.success) {
                const keywordData = response.data;
                // Combine all keyword types
                return [
                    ...keywordData.primaryKeywords,
                    ...keywordData.longTailKeywords.slice(0, 5), // Limit long-tail keywords
                    ...keywordData.semanticKeywords.slice(0, 3)  // Limit semantic keywords
                ];
            } else {
                throw new Error(response.error || 'Keyword generation failed');
            }
        } catch (error) {
            console.error('Keyword generation error:', error);
            // Return fallback keywords
            return [
                'seo optimization',
                'content marketing',
                'digital marketing',
                'search engine optimization',
                'keyword research'
            ];
        }
    }

    updateDashboard(analysis) {
        document.getElementById('seo-score').textContent = analysis.seoScore;
        document.getElementById('keyword-score').textContent = analysis.keywordScore;
        document.getElementById('content-score').textContent = analysis.contentScore;
        document.getElementById('technical-score').textContent = analysis.technicalScore;

        // Update recommendations
        const recommendationsList = document.getElementById('recommendations-list');
        recommendationsList.innerHTML = '';

        analysis.recommendations.forEach(rec => {
            const recElement = document.createElement('div');
            recElement.className = 'recommendation-item';
            recElement.innerHTML = `
                <div class="rec-icon">${this.getRecommendationIcon(rec.type)}</div>
                <div class="rec-content">
                    <div class="rec-title">${rec.title}</div>
                    <div class="rec-description">${rec.description}</div>
                </div>
            `;
            recommendationsList.appendChild(recElement);
        });
    }

    updateAnalysisTab(analysis) {
        // Update analysis tab with detailed results
        if (analysis.detailedAnalysis) {
            const detailed = analysis.detailedAnalysis;

            // Meta Tags Analysis
            this.updateMetaAnalysis(detailed.title, detailed.metaDescription);

            // Headings Analysis
            this.updateHeadingsAnalysis(detailed.headings);

            // Keyword Analysis
            this.updateKeywordAnalysis(detailed.keywords);

            // Content Analysis
            this.updateContentAnalysis(detailed.content, detailed.readability);
        } else {
            // Fallback for basic analysis
            document.getElementById('meta-analysis').innerHTML = '<div class="analysis-result">Basic analysis complete</div>';
            document.getElementById('headings-analysis').innerHTML = '<div class="analysis-result">Basic analysis complete</div>';
            document.getElementById('keyword-analysis').innerHTML = '<div class="analysis-result">Basic analysis complete</div>';
            document.getElementById('content-analysis').innerHTML = '<div class="analysis-result">Basic analysis complete</div>';
        }
    }

    updateMetaAnalysis(titleAnalysis, metaAnalysis) {
        const container = document.getElementById('meta-analysis');
        container.innerHTML = `
            <div class="analysis-section">
                <h5>Title Tag</h5>
                <div class="score-badge score-${this.getScoreClass(titleAnalysis.score)}">${titleAnalysis.score}/100</div>
                <p>Length: ${titleAnalysis.length} characters</p>
                ${titleAnalysis.issues.length > 0 ? `<div class="issues">Issues: ${titleAnalysis.issues.join(', ')}</div>` : ''}
                ${titleAnalysis.suggestions.length > 0 ? `<div class="suggestions">${titleAnalysis.suggestions.slice(0, 2).join('; ')}</div>` : ''}
            </div>
            <div class="analysis-section">
                <h5>Meta Description</h5>
                <div class="score-badge score-${this.getScoreClass(metaAnalysis.score)}">${metaAnalysis.score}/100</div>
                <p>Length: ${metaAnalysis.length} characters</p>
                ${metaAnalysis.issues.length > 0 ? `<div class="issues">Issues: ${metaAnalysis.issues.join(', ')}</div>` : ''}
                ${metaAnalysis.suggestions.length > 0 ? `<div class="suggestions">${metaAnalysis.suggestions.slice(0, 2).join('; ')}</div>` : ''}
            </div>
        `;
    }

    updateHeadingsAnalysis(headingsAnalysis) {
        const container = document.getElementById('headings-analysis');
        const structure = headingsAnalysis.structure;

        container.innerHTML = `
            <div class="analysis-section">
                <div class="score-badge score-${this.getScoreClass(headingsAnalysis.score)}">${headingsAnalysis.score}/100</div>
                <div class="heading-structure">
                    <p>H1: ${structure.h1 || 0} | H2: ${structure.h2 || 0} | H3: ${structure.h3 || 0}</p>
                </div>
                ${headingsAnalysis.issues.length > 0 ? `<div class="issues">Issues: ${headingsAnalysis.issues.join(', ')}</div>` : ''}
                ${headingsAnalysis.suggestions.length > 0 ? `<div class="suggestions">${headingsAnalysis.suggestions.slice(0, 2).join('; ')}</div>` : ''}
            </div>
        `;
    }

    updateKeywordAnalysis(keywordAnalysis) {
        const container = document.getElementById('keyword-analysis');
        const stats = keywordAnalysis.keywordStats || {};

        let keywordHtml = `
            <div class="analysis-section">
                <div class="score-badge score-${this.getScoreClass(keywordAnalysis.overallScore)}">${keywordAnalysis.overallScore}/100</div>
                <p>Total words: ${keywordAnalysis.totalWords}</p>
        `;

        Object.entries(stats).forEach(([keyword, stat]) => {
            keywordHtml += `
                <div class="keyword-stat">
                    <strong>${keyword}:</strong> ${stat.density.toFixed(1)}% (${stat.occurrences} times)
                    <span class="recommendation">${stat.recommendation}</span>
                </div>
            `;
        });

        keywordHtml += '</div>';
        container.innerHTML = keywordHtml;
    }

    updateContentAnalysis(contentAnalysis, readabilityAnalysis) {
        const container = document.getElementById('content-analysis');
        container.innerHTML = `
            <div class="analysis-section">
                <h5>Content Quality</h5>
                <div class="score-badge score-${this.getScoreClass(contentAnalysis.score)}">${contentAnalysis.score}/100</div>
                <p>Word count: ${contentAnalysis.wordCount}</p>
                ${contentAnalysis.issues.length > 0 ? `<div class="issues">Issues: ${contentAnalysis.issues.slice(0, 2).join(', ')}</div>` : ''}
            </div>
            <div class="analysis-section">
                <h5>Readability</h5>
                <div class="score-badge score-${this.getScoreClass(readabilityAnalysis.score)}">${readabilityAnalysis.score}/100</div>
                <p>Grade: ${readabilityAnalysis.grade}</p>
                <p>Avg words/sentence: ${readabilityAnalysis.averageWordsPerSentence}</p>
            </div>
        `;
    }

    getScoreClass(score) {
        if (score >= 80) return 'good';
        if (score >= 60) return 'medium';
        return 'poor';
    }

    getRecommendationIcon(type) {
        const icons = {
            keyword: '🎯',
            meta: '📝',
            content: '📄',
            technical: '⚙️',
            image: '🖼️',
            link: '🔗'
        };
        return icons[type] || '💡';
    }

    updateUI() {
        // Update UI based on current state
    }

    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loading-overlay');
        const text = overlay.querySelector('.loading-text');
        text.textContent = message;
        overlay.classList.remove('hidden');
    }

    hideLoading() {
        document.getElementById('loading-overlay').classList.add('hidden');
    }

    showNotification(message, type = 'info') {
        // Simple notification system - could be enhanced
        console.log(`${type.toUpperCase()}: ${message}`);
        
        // You could implement a toast notification system here
        alert(message);
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new SEOOptimizerPopup();
});
