<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI SEO Optimizer</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">
                <img src="../assets/icons/icon32.png" alt="AI SEO Optimizer" class="logo-icon">
                <h1>AI SEO Optimizer</h1>
            </div>
            <div class="nav-tabs">
                <button class="tab-btn active" data-tab="dashboard">Dashboard</button>
                <button class="tab-btn" data-tab="analysis">Analysis</button>
                <button class="tab-btn" data-tab="settings">Settings</button>
            </div>
        </header>

        <main class="main-content">
            <!-- Dashboard Tab -->
            <div id="dashboard" class="tab-content active">
                <div class="seo-score-card">
                    <div class="score-circle">
                        <div class="score-number" id="seo-score">--</div>
                        <div class="score-label">SEO Score</div>
                    </div>
                    <div class="score-details">
                        <div class="score-item">
                            <span class="score-category">Keywords</span>
                            <span class="score-value" id="keyword-score">--</span>
                        </div>
                        <div class="score-item">
                            <span class="score-category">Content</span>
                            <span class="score-value" id="content-score">--</span>
                        </div>
                        <div class="score-item">
                            <span class="score-category">Technical</span>
                            <span class="score-value" id="technical-score">--</span>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <button class="action-btn primary" id="analyze-page">
                        <span class="btn-icon">🔍</span>
                        Analyze Current Page
                    </button>
                    <button class="action-btn secondary" id="generate-keywords">
                        <span class="btn-icon">🎯</span>
                        Generate Keywords
                    </button>
                </div>

                <div class="recent-analysis">
                    <h3>Recent Recommendations</h3>
                    <div id="recommendations-list" class="recommendations">
                        <div class="recommendation-item">
                            <div class="rec-icon">💡</div>
                            <div class="rec-content">
                                <div class="rec-title">Click "Analyze Current Page" to get started</div>
                                <div class="rec-description">Get AI-powered SEO recommendations for this page</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Tab -->
            <div id="analysis" class="tab-content">
                <div class="analysis-section">
                    <h3>Page Analysis</h3>
                    <div class="analysis-grid">
                        <div class="analysis-card">
                            <h4>Meta Tags</h4>
                            <div id="meta-analysis" class="analysis-content">
                                <div class="loading">Click analyze to see results</div>
                            </div>
                        </div>
                        <div class="analysis-card">
                            <h4>Headings Structure</h4>
                            <div id="headings-analysis" class="analysis-content">
                                <div class="loading">Click analyze to see results</div>
                            </div>
                        </div>
                        <div class="analysis-card">
                            <h4>Keyword Density</h4>
                            <div id="keyword-analysis" class="analysis-content">
                                <div class="loading">Click analyze to see results</div>
                            </div>
                        </div>
                        <div class="analysis-card">
                            <h4>Content Quality</h4>
                            <div id="content-analysis" class="analysis-content">
                                <div class="loading">Click analyze to see results</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings" class="tab-content">
                <div class="settings-section">
                    <div class="section-header">
                        <h3>LLM Providers</h3>
                        <button type="button" id="add-llm-provider" class="btn-secondary">+ Add Provider</button>
                    </div>
                    <div id="llm-providers-list">
                        <!-- LLM providers will be dynamically added here -->
                    </div>
                    <div class="setting-group">
                        <label for="default-provider">Default Provider</label>
                        <select id="default-provider">
                            <option value="">Select a provider...</option>
                        </select>
                        <small>Choose which provider to use by default for AI analysis</small>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>SEO Preferences</h3>
                    <div class="setting-group">
                        <label for="target-keywords">Target Keywords</label>
                        <textarea id="target-keywords" placeholder="Enter target keywords, separated by commas" rows="3"></textarea>
                        <small>Primary keywords you want to optimize for across all pages</small>
                    </div>
                    <div class="setting-group">
                        <label for="keyword-density">Target Keyword Density (%)</label>
                        <input type="range" id="keyword-density" min="1" max="5" value="2" step="0.5">
                        <span id="density-value">2%</span>
                        <small>Optimal keyword density range for content optimization</small>
                    </div>
                    <div class="setting-group">
                        <label for="content-goals">Content Goals</label>
                        <select id="content-goals">
                            <option value="informational">Informational Content</option>
                            <option value="commercial">Commercial/Sales</option>
                            <option value="local">Local Business</option>
                            <option value="blog">Blog/News</option>
                            <option value="ecommerce">E-commerce</option>
                        </select>
                        <small>Type of content you primarily create for better recommendations</small>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Analysis Settings</h3>
                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="auto-analysis">
                            <span class="checkmark"></span>
                            Enable automatic page analysis
                        </label>
                        <small>Automatically analyze pages when they load</small>
                    </div>
                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="highlight-issues">
                            <span class="checkmark"></span>
                            Highlight SEO issues on page
                        </label>
                        <small>Visually highlight SEO problems directly on webpages</small>
                    </div>
                    <div class="setting-group">
                        <label for="analysis-depth">Analysis Depth</label>
                        <select id="analysis-depth">
                            <option value="basic">Basic (Fast)</option>
                            <option value="standard">Standard (Recommended)</option>
                            <option value="comprehensive">Comprehensive (Detailed)</option>
                        </select>
                        <small>Choose between speed and detail level</small>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Website Tracking</h3>
                    <div class="setting-group">
                        <label for="tracked-domains">Tracked Websites</label>
                        <textarea id="tracked-domains" placeholder="example.com, mysite.org" rows="2"></textarea>
                        <small>Domains to track for SEO progress (one per line or comma-separated)</small>
                    </div>
                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="save-history">
                            <span class="checkmark"></span>
                            Save analysis history
                        </label>
                        <small>Keep track of SEO improvements over time</small>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Notifications</h3>
                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="show-badge">
                            <span class="checkmark"></span>
                            Show SEO score badge
                        </label>
                        <small>Display SEO score on extension icon</small>
                    </div>
                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="notify-issues">
                            <span class="checkmark"></span>
                            Notify about critical SEO issues
                        </label>
                        <small>Get alerts for important SEO problems</small>
                    </div>
                </div>

                <div class="settings-actions">
                    <button class="action-btn primary" id="save-settings">Save Settings</button>
                    <button class="action-btn secondary" id="reset-settings">Reset to Default</button>
                    <button class="action-btn secondary" id="export-settings">Export Settings</button>
                    <button class="action-btn secondary" id="import-settings">Import Settings</button>
                </div>

                <input type="file" id="import-file" accept=".json" style="display: none;">
            </div>
        </main>

        <div id="loading-overlay" class="loading-overlay hidden">
            <div class="loading-spinner"></div>
            <div class="loading-text">Analyzing page...</div>
        </div>
    </div>

    <!-- Add Provider Modal -->
    <div id="add-provider-modal" class="modal-overlay hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add LLM Provider</h3>
                <button type="button" class="modal-close" id="close-modal">&times;</button>
            </div>

            <div class="provider-templates">
                <div class="provider-template" data-provider="openai">
                    <div class="template-name">🤖 OpenAI</div>
                    <div class="template-description">GPT-4, GPT-3.5 Turbo and other OpenAI models</div>
                    <div class="template-models">Models: gpt-4, gpt-3.5-turbo, gpt-4-turbo</div>
                </div>

                <div class="provider-template" data-provider="anthropic">
                    <div class="template-name">🧠 Anthropic</div>
                    <div class="template-description">Claude 3 Opus, Sonnet, and Haiku models</div>
                    <div class="template-models">Models: claude-3-opus, claude-3-sonnet, claude-3-haiku</div>
                </div>

                <div class="provider-template" data-provider="openrouter">
                    <div class="template-name">🌐 OpenRouter</div>
                    <div class="template-description">Access to multiple AI models through one API</div>
                    <div class="template-models">Models: Various (GPT-4, Claude, Llama, etc.)</div>
                </div>

                <div class="provider-template" data-provider="ollama">
                    <div class="template-name">🏠 Ollama (Local)</div>
                    <div class="template-description">Run models locally on your machine</div>
                    <div class="template-models">Models: llama2, codellama, mistral, etc.</div>
                </div>

                <div class="provider-template" data-provider="groq">
                    <div class="template-name">⚡ Groq</div>
                    <div class="template-description">Ultra-fast inference with Groq LPU</div>
                    <div class="template-models">Models: llama2-70b, mixtral-8x7b, gemma-7b</div>
                </div>

                <div class="provider-template" data-provider="custom">
                    <div class="template-name">⚙️ Custom Provider</div>
                    <div class="template-description">Configure your own API endpoint</div>
                    <div class="template-models">Custom configuration required</div>
                </div>
            </div>

            <div id="provider-form" class="hidden">
                <div class="setting-group">
                    <label for="provider-name">Provider Name</label>
                    <input type="text" id="provider-name" placeholder="My Custom Provider">
                </div>

                <div class="setting-group">
                    <label for="provider-api-key">API Key</label>
                    <input type="password" id="provider-api-key" placeholder="Enter API key">
                </div>

                <div class="setting-group">
                    <label for="provider-base-url">Base URL</label>
                    <input type="url" id="provider-base-url" placeholder="https://api.example.com/v1">
                </div>

                <div class="setting-group">
                    <label for="provider-model">Default Model</label>
                    <input type="text" id="provider-model" placeholder="gpt-4">
                </div>

                <div class="setting-group">
                    <label for="provider-max-tokens">Max Tokens</label>
                    <input type="number" id="provider-max-tokens" value="4000" min="100" max="32000">
                </div>
            </div>

            <div class="modal-actions">
                <button type="button" id="cancel-add-provider" class="btn-secondary">Cancel</button>
                <button type="button" id="confirm-add-provider" class="btn-primary" disabled>Add Provider</button>
            </div>
        </div>
    </div>

    <script src="popup-simple.js"></script>
</body>
</html>
