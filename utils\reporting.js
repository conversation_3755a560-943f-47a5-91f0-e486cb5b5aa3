// SEO Reporting and Analytics Module

class SEOReporting {
    constructor() {
        this.reportData = {
            history: [],
            trends: {},
            insights: []
        };
    }

    // Generate comprehensive SEO report
    async generateReport(analysisData, pageData, settings = {}) {
        const report = {
            timestamp: new Date().toISOString(),
            url: pageData.url,
            title: pageData.title,
            analysis: analysisData,
            insights: this.generateInsights(analysisData, pageData),
            recommendations: this.prioritizeRecommendations(analysisData.recommendations),
            trends: await this.calculateTrends(pageData.url),
            score: {
                current: analysisData.seoScore,
                previous: await this.getPreviousScore(pageData.url),
                change: 0 // Will be calculated
            }
        };

        // Calculate score change
        if (report.score.previous !== null) {
            report.score.change = report.score.current - report.score.previous;
        }

        return report;
    }

    // Generate actionable insights from analysis data
    generateInsights(analysisData, pageData) {
        const insights = [];

        // Score-based insights
        if (analysisData.seoScore < 50) {
            insights.push({
                type: 'critical',
                title: 'SEO Score Needs Immediate Attention',
                description: 'Your page has significant SEO issues that need to be addressed urgently.',
                impact: 'high',
                effort: 'medium'
            });
        } else if (analysisData.seoScore < 70) {
            insights.push({
                type: 'warning',
                title: 'SEO Performance Can Be Improved',
                description: 'Several optimization opportunities exist to boost your search rankings.',
                impact: 'medium',
                effort: 'low'
            });
        } else if (analysisData.seoScore >= 80) {
            insights.push({
                type: 'success',
                title: 'Excellent SEO Performance',
                description: 'Your page is well-optimized for search engines.',
                impact: 'low',
                effort: 'low'
            });
        }

        // Keyword-specific insights
        if (analysisData.keywordScore < 60) {
            insights.push({
                type: 'opportunity',
                title: 'Keyword Optimization Opportunity',
                description: 'Better keyword integration could significantly improve your rankings.',
                impact: 'high',
                effort: 'low'
            });
        }

        // Content insights
        if (analysisData.contentScore < 60) {
            insights.push({
                type: 'improvement',
                title: 'Content Quality Enhancement Needed',
                description: 'Improving content structure and readability will boost user engagement.',
                impact: 'medium',
                effort: 'medium'
            });
        }

        // Technical insights
        if (analysisData.technicalScore < 70) {
            insights.push({
                type: 'technical',
                title: 'Technical SEO Issues Detected',
                description: 'Address technical issues to improve search engine crawling and indexing.',
                impact: 'high',
                effort: 'high'
            });
        }

        // Detailed analysis insights
        if (analysisData.detailedAnalysis) {
            const detailed = analysisData.detailedAnalysis;
            
            // Image optimization insights
            if (detailed.images && detailed.images.imagesWithoutAlt > 0) {
                insights.push({
                    type: 'accessibility',
                    title: 'Image Accessibility Issues',
                    description: `${detailed.images.imagesWithoutAlt} images are missing alt text, affecting both SEO and accessibility.`,
                    impact: 'medium',
                    effort: 'low'
                });
            }

            // Readability insights
            if (detailed.readability && detailed.readability.score < 60) {
                insights.push({
                    type: 'readability',
                    title: 'Content Readability Needs Improvement',
                    description: 'Simplifying language and sentence structure will improve user experience.',
                    impact: 'medium',
                    effort: 'medium'
                });
            }
        }

        return insights;
    }

    // Prioritize recommendations based on impact and effort
    prioritizeRecommendations(recommendations) {
        const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
        
        return recommendations
            .map(rec => ({
                ...rec,
                priorityScore: priorityOrder[rec.priority] || 1
            }))
            .sort((a, b) => b.priorityScore - a.priorityScore)
            .map(rec => {
                // Add effort estimation
                rec.effort = this.estimateEffort(rec);
                rec.impact = this.estimateImpact(rec);
                return rec;
            });
    }

    // Estimate effort required for a recommendation
    estimateEffort(recommendation) {
        const effortMap = {
            'title': 'low',
            'meta': 'low',
            'heading': 'low',
            'keyword': 'medium',
            'content': 'high',
            'image': 'low',
            'link': 'medium',
            'technical': 'high'
        };

        return effortMap[recommendation.type] || 'medium';
    }

    // Estimate impact of implementing a recommendation
    estimateImpact(recommendation) {
        const impactMap = {
            'title': 'high',
            'meta': 'high',
            'heading': 'medium',
            'keyword': 'high',
            'content': 'medium',
            'image': 'low',
            'link': 'medium',
            'technical': 'high'
        };

        return impactMap[recommendation.type] || 'medium';
    }

    // Calculate trends for a specific URL
    async calculateTrends(url) {
        try {
            const history = await this.getAnalysisHistory(url);
            
            if (history.length < 2) {
                return {
                    available: false,
                    message: 'Not enough data for trend analysis'
                };
            }

            const recent = history.slice(-5); // Last 5 analyses
            const trends = {
                available: true,
                seoScore: this.calculateTrend(recent.map(h => h.analysis.seoScore)),
                keywordScore: this.calculateTrend(recent.map(h => h.analysis.keywordScore)),
                contentScore: this.calculateTrend(recent.map(h => h.analysis.contentScore)),
                technicalScore: this.calculateTrend(recent.map(h => h.analysis.technicalScore)),
                period: `${recent.length} analyses`,
                timespan: this.calculateTimespan(recent[0].timestamp, recent[recent.length - 1].timestamp)
            };

            return trends;
        } catch (error) {
            console.error('Error calculating trends:', error);
            return {
                available: false,
                message: 'Error calculating trends'
            };
        }
    }

    // Calculate trend direction and magnitude
    calculateTrend(values) {
        if (values.length < 2) return { direction: 'stable', change: 0 };

        const first = values[0];
        const last = values[values.length - 1];
        const change = last - first;
        const percentChange = (change / first) * 100;

        let direction = 'stable';
        if (Math.abs(percentChange) > 5) {
            direction = change > 0 ? 'improving' : 'declining';
        }

        return {
            direction,
            change: Math.round(change),
            percentChange: Math.round(percentChange * 10) / 10,
            values: values
        };
    }

    // Calculate timespan between two timestamps
    calculateTimespan(start, end) {
        const startDate = new Date(start);
        const endDate = new Date(end);
        const diffMs = endDate - startDate;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) return 'Same day';
        if (diffDays === 1) return '1 day';
        if (diffDays < 7) return `${diffDays} days`;
        if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks`;
        return `${Math.floor(diffDays / 30)} months`;
    }

    // Get analysis history for a specific URL
    async getAnalysisHistory(url) {
        try {
            const result = await chrome.storage.local.get(['analysisHistory']);
            const allHistory = result.analysisHistory || [];
            
            return allHistory
                .filter(entry => entry.url === url)
                .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
        } catch (error) {
            console.error('Error getting analysis history:', error);
            return [];
        }
    }

    // Get previous score for comparison
    async getPreviousScore(url) {
        try {
            const history = await this.getAnalysisHistory(url);
            if (history.length < 2) return null;
            
            return history[history.length - 2].analysis.seoScore;
        } catch (error) {
            console.error('Error getting previous score:', error);
            return null;
        }
    }

    // Generate visual report data for charts
    generateChartData(analysisData, trends) {
        const chartData = {
            scoreBreakdown: {
                labels: ['Keywords', 'Content', 'Technical'],
                data: [
                    analysisData.keywordScore,
                    analysisData.contentScore,
                    analysisData.technicalScore
                ],
                colors: ['#667eea', '#764ba2', '#f093fb']
            },
            trendChart: null
        };

        // Add trend chart data if available
        if (trends.available) {
            chartData.trendChart = {
                labels: trends.seoScore.values.map((_, index) => `Analysis ${index + 1}`),
                datasets: [
                    {
                        label: 'SEO Score',
                        data: trends.seoScore.values,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)'
                    },
                    {
                        label: 'Keyword Score',
                        data: trends.keywordScore.values,
                        borderColor: '#764ba2',
                        backgroundColor: 'rgba(118, 75, 162, 0.1)'
                    }
                ]
            };
        }

        return chartData;
    }

    // Export report data
    exportReport(reportData, format = 'json') {
        const timestamp = new Date().toISOString().split('T')[0];
        const filename = `seo-report-${timestamp}`;

        switch (format) {
            case 'json':
                return this.exportJSON(reportData, filename);
            case 'csv':
                return this.exportCSV(reportData, filename);
            default:
                throw new Error('Unsupported export format');
        }
    }

    // Export as JSON
    exportJSON(data, filename) {
        const jsonStr = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `${filename}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }

    // Export as CSV (simplified)
    exportCSV(data, filename) {
        const csvData = [
            ['Metric', 'Score', 'Status'],
            ['Overall SEO', data.analysis.seoScore, this.getScoreStatus(data.analysis.seoScore)],
            ['Keywords', data.analysis.keywordScore, this.getScoreStatus(data.analysis.keywordScore)],
            ['Content', data.analysis.contentScore, this.getScoreStatus(data.analysis.contentScore)],
            ['Technical', data.analysis.technicalScore, this.getScoreStatus(data.analysis.technicalScore)]
        ];

        const csvStr = csvData.map(row => row.join(',')).join('\n');
        const blob = new Blob([csvStr], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `${filename}.csv`;
        link.click();
        
        URL.revokeObjectURL(url);
    }

    // Get status label for score
    getScoreStatus(score) {
        if (score >= 80) return 'Excellent';
        if (score >= 60) return 'Good';
        if (score >= 40) return 'Needs Improvement';
        return 'Critical';
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SEOReporting;
} else if (typeof window !== 'undefined') {
    window.SEOReporting = SEOReporting;
}
