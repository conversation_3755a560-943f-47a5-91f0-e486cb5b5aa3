// SEO Analysis Module - Advanced SEO Analysis Functions

class SEOAnalyzer {
    constructor() {
        this.stopWords = new Set([
            'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
            'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
            'to', 'was', 'will', 'with', 'but', 'or', 'not', 'this', 'they',
            'have', 'had', 'what', 'said', 'each', 'which', 'their', 'time',
            'if', 'up', 'out', 'many', 'then', 'them', 'these', 'so', 'some',
            'her', 'would', 'make', 'like', 'into', 'him', 'has', 'two',
            'more', 'very', 'after', 'words', 'long', 'than', 'first', 'been',
            'call', 'who', 'oil', 'sit', 'now', 'find', 'down', 'day', 'did',
            'get', 'come', 'made', 'may', 'part'
        ]);
    }

    // Comprehensive page analysis
    analyzePageSEO(pageData, targetKeywords = []) {
        const analysis = {
            overall: this.calculateOverallScore(pageData, targetKeywords),
            title: this.analyzeTitleTag(pageData.title, targetKeywords),
            metaDescription: this.analyzeMetaDescription(pageData.metaDescription, targetKeywords),
            headings: this.analyzeHeadingStructure(pageData.headings, targetKeywords),
            content: this.analyzeContent(pageData.content, targetKeywords),
            keywords: this.analyzeKeywordDensity(pageData.content, targetKeywords),
            images: this.analyzeImages(pageData.images),
            links: this.analyzeLinks(pageData.links),
            technical: this.analyzeTechnicalSEO(pageData),
            readability: this.analyzeReadability(pageData.content),
            recommendations: []
        };

        // Generate recommendations based on analysis
        analysis.recommendations = this.generateRecommendations(analysis, pageData, targetKeywords);

        return analysis;
    }

    // Title tag analysis
    analyzeTitleTag(title, targetKeywords = []) {
        const analysis = {
            score: 0,
            length: title ? title.length : 0,
            issues: [],
            suggestions: []
        };

        if (!title) {
            analysis.issues.push('Missing title tag');
            analysis.suggestions.push('Add a descriptive title tag');
            return analysis;
        }

        // Length analysis
        if (title.length < 30) {
            analysis.issues.push('Title too short');
            analysis.suggestions.push('Expand title to 30-60 characters');
        } else if (title.length > 60) {
            analysis.issues.push('Title too long');
            analysis.suggestions.push('Shorten title to under 60 characters');
        } else {
            analysis.score += 30;
        }

        // Keyword analysis
        if (targetKeywords.length > 0) {
            const titleLower = title.toLowerCase();
            const keywordsFound = targetKeywords.filter(keyword => 
                titleLower.includes(keyword.toLowerCase())
            );
            
            if (keywordsFound.length > 0) {
                analysis.score += 40;
                analysis.suggestions.push(`Good: Contains keywords: ${keywordsFound.join(', ')}`);
            } else {
                analysis.issues.push('No target keywords in title');
                analysis.suggestions.push('Include primary keyword in title');
            }
        }

        // Position of primary keyword
        if (targetKeywords.length > 0) {
            const primaryKeyword = targetKeywords[0].toLowerCase();
            const titleLower = title.toLowerCase();
            const keywordPosition = titleLower.indexOf(primaryKeyword);
            
            if (keywordPosition === 0) {
                analysis.score += 20;
                analysis.suggestions.push('Excellent: Primary keyword at beginning');
            } else if (keywordPosition > 0 && keywordPosition < title.length / 2) {
                analysis.score += 10;
                analysis.suggestions.push('Good: Primary keyword in first half');
            }
        }

        // Uniqueness check (basic)
        if (title.includes('|') || title.includes('-')) {
            analysis.score += 10;
            analysis.suggestions.push('Good: Uses separators for branding');
        }

        return analysis;
    }

    // Meta description analysis
    analyzeMetaDescription(metaDescription, targetKeywords = []) {
        const analysis = {
            score: 0,
            length: metaDescription ? metaDescription.length : 0,
            issues: [],
            suggestions: []
        };

        if (!metaDescription) {
            analysis.issues.push('Missing meta description');
            analysis.suggestions.push('Add a compelling meta description');
            return analysis;
        }

        // Length analysis
        if (metaDescription.length < 120) {
            analysis.issues.push('Meta description too short');
            analysis.suggestions.push('Expand to 120-160 characters');
        } else if (metaDescription.length > 160) {
            analysis.issues.push('Meta description too long');
            analysis.suggestions.push('Shorten to under 160 characters');
        } else {
            analysis.score += 40;
        }

        // Keyword analysis
        if (targetKeywords.length > 0) {
            const descLower = metaDescription.toLowerCase();
            const keywordsFound = targetKeywords.filter(keyword => 
                descLower.includes(keyword.toLowerCase())
            );
            
            if (keywordsFound.length > 0) {
                analysis.score += 30;
                analysis.suggestions.push(`Good: Contains keywords: ${keywordsFound.join(', ')}`);
            } else {
                analysis.issues.push('No target keywords in meta description');
                analysis.suggestions.push('Include primary keyword naturally');
            }
        }

        // Call-to-action analysis
        const ctaWords = ['learn', 'discover', 'find', 'get', 'download', 'buy', 'try', 'start'];
        const hasCallToAction = ctaWords.some(word => 
            metaDescription.toLowerCase().includes(word)
        );
        
        if (hasCallToAction) {
            analysis.score += 20;
            analysis.suggestions.push('Good: Contains call-to-action');
        } else {
            analysis.suggestions.push('Consider adding a call-to-action');
        }

        // Uniqueness indicators
        if (metaDescription.includes('...') || metaDescription.endsWith('.')) {
            analysis.score += 10;
        }

        return analysis;
    }

    // Heading structure analysis
    analyzeHeadingStructure(headings, targetKeywords = []) {
        const analysis = {
            score: 0,
            structure: {},
            issues: [],
            suggestions: []
        };

        // Count headings by level
        Object.keys(headings).forEach(level => {
            analysis.structure[level] = headings[level].length;
        });

        // H1 analysis
        if (headings.h1.length === 0) {
            analysis.issues.push('Missing H1 tag');
            analysis.suggestions.push('Add exactly one H1 tag');
        } else if (headings.h1.length === 1) {
            analysis.score += 30;
            analysis.suggestions.push('Good: Single H1 tag found');
            
            // Check if H1 contains keywords
            if (targetKeywords.length > 0) {
                const h1Text = headings.h1[0].text.toLowerCase();
                const keywordsInH1 = targetKeywords.filter(keyword => 
                    h1Text.includes(keyword.toLowerCase())
                );
                
                if (keywordsInH1.length > 0) {
                    analysis.score += 20;
                    analysis.suggestions.push('Excellent: H1 contains target keywords');
                } else {
                    analysis.suggestions.push('Consider including primary keyword in H1');
                }
            }
        } else {
            analysis.issues.push('Multiple H1 tags found');
            analysis.suggestions.push('Use only one H1 tag per page');
        }

        // H2-H6 structure analysis
        if (headings.h2.length > 0) {
            analysis.score += 20;
            analysis.suggestions.push('Good: Uses H2 subheadings');
            
            // Check keyword usage in H2s
            if (targetKeywords.length > 0) {
                const h2WithKeywords = headings.h2.filter(h2 => 
                    targetKeywords.some(keyword => 
                        h2.text.toLowerCase().includes(keyword.toLowerCase())
                    )
                );
                
                if (h2WithKeywords.length > 0) {
                    analysis.score += 15;
                    analysis.suggestions.push('Good: H2 tags contain keywords');
                }
            }
        } else {
            analysis.suggestions.push('Consider adding H2 subheadings for better structure');
        }

        // Hierarchy check
        const hasProperHierarchy = this.checkHeadingHierarchy(headings);
        if (hasProperHierarchy) {
            analysis.score += 15;
            analysis.suggestions.push('Good: Proper heading hierarchy');
        } else {
            analysis.issues.push('Improper heading hierarchy');
            analysis.suggestions.push('Maintain proper H1 → H2 → H3 hierarchy');
        }

        return analysis;
    }

    // Content analysis
    analyzeContent(content, targetKeywords = []) {
        const text = typeof content === 'string' ? content : content.text || '';
        const words = this.extractWords(text);
        const wordCount = words.length;
        
        const analysis = {
            score: 0,
            wordCount: wordCount,
            readabilityScore: this.calculateReadabilityScore(text),
            keywordDensity: {},
            issues: [],
            suggestions: []
        };

        // Word count analysis
        if (wordCount < 300) {
            analysis.issues.push('Content too short');
            analysis.suggestions.push('Aim for at least 300 words');
        } else if (wordCount >= 300 && wordCount < 1000) {
            analysis.score += 20;
            analysis.suggestions.push('Good: Adequate content length');
        } else {
            analysis.score += 30;
            analysis.suggestions.push('Excellent: Comprehensive content');
        }

        // Keyword density analysis
        if (targetKeywords.length > 0) {
            targetKeywords.forEach(keyword => {
                const density = this.calculateKeywordDensity(text, keyword);
                analysis.keywordDensity[keyword] = density;
                
                if (density === 0) {
                    analysis.issues.push(`Keyword "${keyword}" not found in content`);
                    analysis.suggestions.push(`Include "${keyword}" naturally in content`);
                } else if (density < 1) {
                    analysis.suggestions.push(`Consider increasing "${keyword}" usage (current: ${density.toFixed(1)}%)`);
                } else if (density >= 1 && density <= 3) {
                    analysis.score += 20;
                    analysis.suggestions.push(`Good keyword density for "${keyword}": ${density.toFixed(1)}%`);
                } else {
                    analysis.issues.push(`Keyword "${keyword}" overused (${density.toFixed(1)}%)`);
                    analysis.suggestions.push(`Reduce "${keyword}" usage to avoid keyword stuffing`);
                }
            });
        }

        // Content structure analysis
        const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);
        if (paragraphs.length < 3) {
            analysis.suggestions.push('Break content into more paragraphs for better readability');
        } else {
            analysis.score += 10;
        }

        return analysis;
    }

    // Keyword density calculation
    analyzeKeywordDensity(content, targetKeywords = []) {
        const text = typeof content === 'string' ? content : content.text || '';
        const words = this.extractWords(text);
        const totalWords = words.length;
        
        const analysis = {
            totalWords: totalWords,
            keywordStats: {},
            overallScore: 0
        };

        targetKeywords.forEach(keyword => {
            const density = this.calculateKeywordDensity(text, keyword);
            const occurrences = this.countKeywordOccurrences(text, keyword);
            
            analysis.keywordStats[keyword] = {
                occurrences: occurrences,
                density: density,
                score: this.scoreKeywordDensity(density),
                recommendation: this.getKeywordDensityRecommendation(density)
            };
        });

        // Calculate overall keyword score
        const scores = Object.values(analysis.keywordStats).map(stat => stat.score);
        analysis.overallScore = scores.length > 0 ? 
            Math.round(scores.reduce((a, b) => a + b, 0) / scores.length) : 0;

        return analysis;
    }

    // Image analysis
    analyzeImages(images) {
        const analysis = {
            score: 0,
            totalImages: images.length,
            imagesWithAlt: 0,
            imagesWithoutAlt: 0,
            issues: [],
            suggestions: []
        };

        if (images.length === 0) {
            analysis.suggestions.push('Consider adding relevant images to enhance content');
            return analysis;
        }

        images.forEach(img => {
            if (img.alt && img.alt.trim().length > 0) {
                analysis.imagesWithAlt++;
            } else {
                analysis.imagesWithoutAlt++;
            }
        });

        // Alt text analysis
        const altTextPercentage = (analysis.imagesWithAlt / analysis.totalImages) * 100;
        
        if (altTextPercentage === 100) {
            analysis.score += 40;
            analysis.suggestions.push('Excellent: All images have alt text');
        } else if (altTextPercentage >= 80) {
            analysis.score += 30;
            analysis.suggestions.push('Good: Most images have alt text');
        } else if (altTextPercentage >= 50) {
            analysis.score += 15;
            analysis.issues.push('Some images missing alt text');
            analysis.suggestions.push('Add alt text to remaining images');
        } else {
            analysis.issues.push('Many images missing alt text');
            analysis.suggestions.push('Add descriptive alt text to all images');
        }

        // File size and format analysis (basic)
        const largeImages = images.filter(img => img.width > 1200 || img.height > 1200);
        if (largeImages.length > 0) {
            analysis.suggestions.push('Consider optimizing large images for faster loading');
        }

        return analysis;
    }

    // Link analysis
    analyzeLinks(links) {
        const analysis = {
            score: 0,
            totalLinks: links.length,
            internalLinks: 0,
            externalLinks: 0,
            noFollowLinks: 0,
            emptyLinks: 0,
            issues: [],
            suggestions: []
        };

        if (links.length === 0) {
            analysis.suggestions.push('Consider adding relevant internal and external links');
            return analysis;
        }

        links.forEach(link => {
            if (!link.href || link.href === '#' || link.href === '') {
                analysis.emptyLinks++;
            } else if (link.isInternal) {
                analysis.internalLinks++;
            } else if (link.isExternal) {
                analysis.externalLinks++;
                if (link.hasNoFollow) {
                    analysis.noFollowLinks++;
                }
            }
        });

        // Internal linking analysis
        if (analysis.internalLinks > 0) {
            analysis.score += 20;
            analysis.suggestions.push('Good: Contains internal links');
        } else {
            analysis.suggestions.push('Add internal links to related content');
        }

        // External linking analysis
        if (analysis.externalLinks > 0) {
            analysis.score += 15;
            analysis.suggestions.push('Good: Contains external links');
            
            // Check for proper nofollow usage
            const externalWithoutNofollow = analysis.externalLinks - analysis.noFollowLinks;
            if (externalWithoutNofollow > 0) {
                analysis.suggestions.push('Consider adding rel="nofollow" to untrusted external links');
            }
        }

        // Empty links analysis
        if (analysis.emptyLinks > 0) {
            analysis.issues.push(`${analysis.emptyLinks} empty or invalid links found`);
            analysis.suggestions.push('Fix or remove empty links');
        } else {
            analysis.score += 15;
        }

        return analysis;
    }

    // Technical SEO analysis
    analyzeTechnicalSEO(pageData) {
        const analysis = {
            score: 0,
            issues: [],
            suggestions: []
        };

        // URL analysis
        if (pageData.url) {
            const url = new URL(pageData.url);
            
            // HTTPS check
            if (url.protocol === 'https:') {
                analysis.score += 20;
                analysis.suggestions.push('Good: Uses HTTPS');
            } else {
                analysis.issues.push('Not using HTTPS');
                analysis.suggestions.push('Implement HTTPS for security');
            }
            
            // URL structure
            if (url.pathname.includes('_') || url.pathname.includes('%20')) {
                analysis.issues.push('URL contains underscores or spaces');
                analysis.suggestions.push('Use hyphens instead of underscores in URLs');
            } else {
                analysis.score += 10;
            }
        }

        // Canonical URL check
        if (pageData.canonicalUrl) {
            analysis.score += 15;
            analysis.suggestions.push('Good: Has canonical URL');
        } else {
            analysis.suggestions.push('Consider adding canonical URL');
        }

        // Structured data check
        if (pageData.structuredData && pageData.structuredData.length > 0) {
            analysis.score += 20;
            analysis.suggestions.push('Excellent: Contains structured data');
        } else {
            analysis.suggestions.push('Consider adding structured data markup');
        }

        // Open Graph check
        if (pageData.openGraph && Object.keys(pageData.openGraph).length > 0) {
            analysis.score += 15;
            analysis.suggestions.push('Good: Has Open Graph tags');
        } else {
            analysis.suggestions.push('Add Open Graph tags for social sharing');
        }

        return analysis;
    }

    // Readability analysis
    analyzeReadability(content) {
        const text = typeof content === 'string' ? content : content.text || '';
        
        const analysis = {
            score: this.calculateReadabilityScore(text),
            averageWordsPerSentence: this.calculateAverageWordsPerSentence(text),
            averageSyllablesPerWord: this.calculateAverageSyllablesPerWord(text),
            grade: '',
            suggestions: []
        };

        // Determine reading grade
        if (analysis.score >= 90) {
            analysis.grade = 'Very Easy (5th grade)';
            analysis.suggestions.push('Excellent: Very easy to read');
        } else if (analysis.score >= 80) {
            analysis.grade = 'Easy (6th grade)';
            analysis.suggestions.push('Good: Easy to read');
        } else if (analysis.score >= 70) {
            analysis.grade = 'Fairly Easy (7th grade)';
            analysis.suggestions.push('Good: Fairly easy to read');
        } else if (analysis.score >= 60) {
            analysis.grade = 'Standard (8th-9th grade)';
            analysis.suggestions.push('Acceptable: Standard reading level');
        } else if (analysis.score >= 50) {
            analysis.grade = 'Fairly Difficult (10th-12th grade)';
            analysis.suggestions.push('Consider simplifying language');
        } else if (analysis.score >= 30) {
            analysis.grade = 'Difficult (College level)';
            analysis.suggestions.push('Simplify sentences and vocabulary');
        } else {
            analysis.grade = 'Very Difficult (Graduate level)';
            analysis.suggestions.push('Significantly simplify content for better readability');
        }

        return analysis;
    }

    // Helper methods
    extractWords(text) {
        return text.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 0 && !this.stopWords.has(word));
    }

    calculateKeywordDensity(text, keyword) {
        const words = this.extractWords(text);
        const keywordWords = keyword.toLowerCase().split(/\s+/);
        const totalWords = words.length;
        
        if (totalWords === 0) return 0;
        
        let occurrences = 0;
        for (let i = 0; i <= words.length - keywordWords.length; i++) {
            const phrase = words.slice(i, i + keywordWords.length).join(' ');
            if (phrase === keywordWords.join(' ')) {
                occurrences++;
            }
        }
        
        return (occurrences / totalWords) * 100;
    }

    countKeywordOccurrences(text, keyword) {
        const regex = new RegExp(keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
        const matches = text.match(regex);
        return matches ? matches.length : 0;
    }

    scoreKeywordDensity(density) {
        if (density === 0) return 0;
        if (density >= 1 && density <= 3) return 100;
        if (density > 3 && density <= 5) return 70;
        if (density > 5) return 30;
        return 50; // Less than 1%
    }

    getKeywordDensityRecommendation(density) {
        if (density === 0) return 'Add this keyword to your content';
        if (density < 1) return 'Consider increasing keyword usage';
        if (density >= 1 && density <= 3) return 'Optimal keyword density';
        if (density > 3 && density <= 5) return 'Slightly high - consider reducing';
        return 'Too high - reduce to avoid keyword stuffing';
    }

    calculateReadabilityScore(text) {
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
        const words = text.split(/\s+/).filter(w => w.length > 0).length;
        const syllables = this.countSyllables(text);
        
        if (sentences === 0 || words === 0) return 0;
        
        // Flesch Reading Ease Score
        const score = 206.835 - (1.015 * (words / sentences)) - (84.6 * (syllables / words));
        return Math.max(0, Math.min(100, Math.round(score)));
    }

    calculateAverageWordsPerSentence(text) {
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
        const words = text.split(/\s+/).filter(w => w.length > 0).length;
        return sentences > 0 ? Math.round((words / sentences) * 10) / 10 : 0;
    }

    calculateAverageSyllablesPerWord(text) {
        const words = text.split(/\s+/).filter(w => w.length > 0);
        if (words.length === 0) return 0;
        
        const totalSyllables = words.reduce((total, word) => {
            return total + this.countSyllablesInWord(word);
        }, 0);
        
        return Math.round((totalSyllables / words.length) * 10) / 10;
    }

    countSyllables(text) {
        const words = text.split(/\s+/).filter(w => w.length > 0);
        return words.reduce((total, word) => {
            return total + this.countSyllablesInWord(word);
        }, 0);
    }

    countSyllablesInWord(word) {
        word = word.toLowerCase().replace(/[^a-z]/g, '');
        if (word.length === 0) return 0;
        
        // Count vowel groups
        const vowelGroups = word.match(/[aeiouy]+/g);
        let syllables = vowelGroups ? vowelGroups.length : 0;
        
        // Adjust for silent e
        if (word.endsWith('e') && syllables > 1) {
            syllables--;
        }
        
        // Ensure at least 1 syllable
        return Math.max(1, syllables);
    }

    checkHeadingHierarchy(headings) {
        // Simple hierarchy check - can be enhanced
        const levels = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
        let previousLevel = 0;
        
        for (const level of levels) {
            const currentLevel = parseInt(level.substring(1));
            if (headings[level].length > 0) {
                if (previousLevel > 0 && currentLevel > previousLevel + 1) {
                    return false; // Skipped a level
                }
                previousLevel = currentLevel;
            }
        }
        
        return true;
    }

    calculateOverallScore(pageData, targetKeywords) {
        // This would be implemented to calculate an overall SEO score
        // based on all the individual analysis components
        return 75; // Placeholder
    }

    generateRecommendations(analysis, pageData, targetKeywords) {
        const recommendations = [];
        
        // Collect recommendations from all analysis components
        if (analysis.title.issues.length > 0) {
            recommendations.push({
                type: 'title',
                title: 'Title Tag Issues',
                description: analysis.title.issues.join(', '),
                priority: 'high'
            });
        }
        
        if (analysis.metaDescription.issues.length > 0) {
            recommendations.push({
                type: 'meta',
                title: 'Meta Description Issues',
                description: analysis.metaDescription.issues.join(', '),
                priority: 'high'
            });
        }
        
        if (analysis.headings.issues.length > 0) {
            recommendations.push({
                type: 'heading',
                title: 'Heading Structure Issues',
                description: analysis.headings.issues.join(', '),
                priority: 'medium'
            });
        }
        
        if (analysis.images.issues.length > 0) {
            recommendations.push({
                type: 'image',
                title: 'Image Optimization Issues',
                description: analysis.images.issues.join(', '),
                priority: 'medium'
            });
        }
        
        if (analysis.links.issues.length > 0) {
            recommendations.push({
                type: 'link',
                title: 'Link Issues',
                description: analysis.links.issues.join(', '),
                priority: 'low'
            });
        }
        
        return recommendations.slice(0, 10); // Limit to top 10
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SEOAnalyzer;
} else if (typeof window !== 'undefined') {
    window.SEOAnalyzer = SEOAnalyzer;
}
