// AI Integration Module for SEO Optimizer

class AIIntegration {
    constructor() {
        this.openaiApiKey = '';
        this.openrouterApiKey = '';
        this.baseUrls = {
            openai: 'https://api.openai.com/v1',
            openrouter: 'https://openrouter.ai/api/v1'
        };
    }

    async initialize() {
        const settings = await chrome.storage.sync.get(['openaiKey', 'openrouterKey']);
        this.openaiApiKey = settings.openaiKey || '';
        this.openrouterApiKey = settings.openrouterKey || '';
    }

    async analyzeContent(pageData, targetKeywords = []) {
        try {
            const prompt = this.buildAnalysisPrompt(pageData, targetKeywords);
            const response = await this.callAI(prompt, 'analysis');
            return this.parseAnalysisResponse(response);
        } catch (error) {
            console.error('Error analyzing content:', error);
            throw new Error('Failed to analyze content with AI');
        }
    }

    async generateKeywords(pageData) {
        try {
            const prompt = this.buildKeywordPrompt(pageData);
            const response = await this.callAI(prompt, 'keywords');
            return this.parseKeywordResponse(response);
        } catch (error) {
            console.error('Error generating keywords:', error);
            throw new Error('Failed to generate keywords with AI');
        }
    }

    async optimizeContent(content, targetKeywords, contentType = 'general') {
        try {
            const prompt = this.buildOptimizationPrompt(content, targetKeywords, contentType);
            const response = await this.callAI(prompt, 'optimization');
            return this.parseOptimizationResponse(response);
        } catch (error) {
            console.error('Error optimizing content:', error);
            throw new Error('Failed to optimize content with AI');
        }
    }

    async generateMetaDescription(pageData, targetKeywords = []) {
        try {
            const prompt = this.buildMetaDescriptionPrompt(pageData, targetKeywords);
            const response = await this.callAI(prompt, 'meta');
            return this.parseMetaResponse(response);
        } catch (error) {
            console.error('Error generating meta description:', error);
            throw new Error('Failed to generate meta description');
        }
    }

    buildAnalysisPrompt(pageData, targetKeywords) {
        return `Analyze this webpage for SEO optimization opportunities:

URL: ${pageData.url}
Title: ${pageData.title}
Meta Description: ${pageData.metaDescription || 'None'}
Target Keywords: ${targetKeywords.join(', ') || 'None specified'}

Content Preview: ${pageData.content.substring(0, 2000)}...

Headings:
H1: ${pageData.headings.h1.join(', ')}
H2: ${pageData.headings.h2.join(', ')}
H3: ${pageData.headings.h3.join(', ')}

Please provide:
1. Overall SEO score (0-100)
2. Keyword optimization score (0-100)
3. Content quality score (0-100)
4. Technical SEO score (0-100)
5. Top 5 specific recommendations with priority levels (high/medium/low)

Format your response as JSON with this structure:
{
  "seoScore": number,
  "keywordScore": number,
  "contentScore": number,
  "technicalScore": number,
  "recommendations": [
    {
      "type": "keyword|meta|content|technical|image|link",
      "title": "Brief title",
      "description": "Detailed description",
      "priority": "high|medium|low"
    }
  ]
}`;
    }

    buildKeywordPrompt(pageData) {
        return `Generate SEO keywords for this webpage:

URL: ${pageData.url}
Title: ${pageData.title}
Content: ${pageData.content.substring(0, 3000)}...

Please analyze the content and suggest:
1. 5-10 primary keywords (high search volume, high relevance)
2. 10-15 long-tail keywords (lower competition, specific intent)
3. 5-10 related semantic keywords

Consider:
- Content context and topic
- User search intent
- Keyword difficulty and competition
- Semantic relationships

Format as JSON:
{
  "primaryKeywords": ["keyword1", "keyword2", ...],
  "longTailKeywords": ["long tail keyword 1", ...],
  "semanticKeywords": ["related keyword 1", ...]
}`;
    }

    buildOptimizationPrompt(content, targetKeywords, contentType) {
        return `Optimize this ${contentType} content for SEO:

Target Keywords: ${targetKeywords.join(', ')}
Current Content: ${content}

Please provide:
1. Optimized version of the content
2. Keyword density recommendations
3. Readability improvements
4. Structure suggestions

Maintain the original meaning while improving SEO performance.

Format as JSON:
{
  "optimizedContent": "improved content here",
  "keywordDensity": {
    "keyword1": "2.5%",
    "keyword2": "1.8%"
  },
  "improvements": ["improvement 1", "improvement 2", ...],
  "readabilityScore": number
}`;
    }

    buildMetaDescriptionPrompt(pageData, targetKeywords) {
        return `Generate an SEO-optimized meta description for this page:

Title: ${pageData.title}
Content: ${pageData.content.substring(0, 1000)}...
Target Keywords: ${targetKeywords.join(', ')}

Requirements:
- 120-160 characters
- Include primary keyword naturally
- Compelling and click-worthy
- Accurately describe the page content

Format as JSON:
{
  "metaDescription": "optimized meta description here",
  "length": number,
  "keywordsIncluded": ["keyword1", "keyword2"]
}`;
    }

    async callAI(prompt, type = 'general') {
        // Try OpenAI first, then OpenRouter as fallback
        if (this.openaiApiKey) {
            try {
                return await this.callOpenAI(prompt, type);
            } catch (error) {
                console.warn('OpenAI failed, trying OpenRouter:', error);
                if (this.openrouterApiKey) {
                    return await this.callOpenRouter(prompt, type);
                }
                throw error;
            }
        } else if (this.openrouterApiKey) {
            return await this.callOpenRouter(prompt, type);
        } else {
            throw new Error('No AI API keys configured');
        }
    }

    async callOpenAI(prompt, type) {
        const response = await fetch(`${this.baseUrls.openai}/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.openaiApiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: 'gpt-4',
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert SEO analyst and content optimizer. Provide accurate, actionable SEO advice in the requested JSON format.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.3,
                max_tokens: 2000
            })
        });

        if (!response.ok) {
            throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data.choices[0].message.content;
    }

    async callOpenRouter(prompt, type) {
        const response = await fetch(`${this.baseUrls.openrouter}/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.openrouterApiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': chrome.runtime.getURL(''),
                'X-Title': 'AI SEO Optimizer'
            },
            body: JSON.stringify({
                model: 'openai/gpt-4',
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert SEO analyst and content optimizer. Provide accurate, actionable SEO advice in the requested JSON format.'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.3,
                max_tokens: 2000
            })
        });

        if (!response.ok) {
            throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        return data.choices[0].message.content;
    }

    parseAnalysisResponse(response) {
        try {
            // Extract JSON from response (in case there's extra text)
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No JSON found in response');
            }
            
            const parsed = JSON.parse(jsonMatch[0]);
            
            // Validate required fields
            if (typeof parsed.seoScore !== 'number' || 
                typeof parsed.keywordScore !== 'number' ||
                typeof parsed.contentScore !== 'number' ||
                typeof parsed.technicalScore !== 'number' ||
                !Array.isArray(parsed.recommendations)) {
                throw new Error('Invalid response format');
            }
            
            return parsed;
        } catch (error) {
            console.error('Error parsing analysis response:', error);
            // Return fallback response
            return {
                seoScore: 50,
                keywordScore: 50,
                contentScore: 50,
                technicalScore: 50,
                recommendations: [
                    {
                        type: 'general',
                        title: 'AI Analysis Error',
                        description: 'Unable to parse AI response. Please check your API configuration.',
                        priority: 'high'
                    }
                ]
            };
        }
    }

    parseKeywordResponse(response) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No JSON found in response');
            }
            
            const parsed = JSON.parse(jsonMatch[0]);
            
            return {
                primaryKeywords: parsed.primaryKeywords || [],
                longTailKeywords: parsed.longTailKeywords || [],
                semanticKeywords: parsed.semanticKeywords || []
            };
        } catch (error) {
            console.error('Error parsing keyword response:', error);
            return {
                primaryKeywords: ['seo', 'optimization', 'content'],
                longTailKeywords: ['seo optimization tips', 'content marketing strategy'],
                semanticKeywords: ['search engine', 'digital marketing', 'web content']
            };
        }
    }

    parseOptimizationResponse(response) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No JSON found in response');
            }
            
            return JSON.parse(jsonMatch[0]);
        } catch (error) {
            console.error('Error parsing optimization response:', error);
            return {
                optimizedContent: 'Error optimizing content',
                keywordDensity: {},
                improvements: ['Check API configuration'],
                readabilityScore: 0
            };
        }
    }

    parseMetaResponse(response) {
        try {
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('No JSON found in response');
            }
            
            return JSON.parse(jsonMatch[0]);
        } catch (error) {
            console.error('Error parsing meta response:', error);
            return {
                metaDescription: 'Error generating meta description',
                length: 0,
                keywordsIncluded: []
            };
        }
    }

    // Utility method to check if AI is available
    isAvailable() {
        return !!(this.openaiApiKey || this.openrouterApiKey);
    }

    // Get available AI provider
    getProvider() {
        if (this.openaiApiKey) return 'OpenAI';
        if (this.openrouterApiKey) return 'OpenRouter';
        return 'None';
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AIIntegration;
} else if (typeof window !== 'undefined') {
    window.AIIntegration = AIIntegration;
}
